'''异常检测   二分类'''
import torch
import torch.nn as nn
import torch.optim as optim
import torch.utils.data
from sklearn.model_selection import train_test_split, KFold
from sklearn.preprocessing import LabelEncoder
from torch.utils.data import TensorDataset, DataLoader, Subset
import pandas as pd
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix
import matplotlib.pyplot as plt
import logging
from copy import deepcopy
import json
import math
from pathlib import Path
import os
import numpy as np
from kitsune import engine
from kitsune.data import FileFormat, build_input_data_pipe
from kitsune.models import FeatureMapper, Kitsune
from kitsune.show import show
import pdb


# 定义全局常量
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
detect_bin_model_path = "Models/CIC-CNN-BiLSTM-known-BIN.pth"
# BATCH_SIZE = 2048
BATCH_SIZE = 2048*8
LEARNING_RATE = 0.001
NUM_EPOCHS = 10
RANDOM_STATE = 42
DEVICE = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s',
                    filename='Logs/PGD-Advtrain-CIC-CNN-BiLSTM-known/test_bin.log',  # 日志文件名
                    filemode='w')
console = logging.StreamHandler()
console.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
console.setFormatter(formatter)
logging.getLogger('').addHandler(console)


def load_data(csv_path):
    # 读取CSV文件
    try:
        df = pd.read_csv(csv_path)
    except Exception as e:
        print(f"Error loading data: {e}")
        return None, None, None

    # 提取特征和标签
    X = df.iloc[:, :-1].values
    y = df.iloc[:, -1].values

    # 将标签进行数字编码
    label_encoder = LabelEncoder()
    y_encoded = label_encoder.fit_transform(y)

    y_encoded = (y_encoded != 0).astype(int) #是否二分类
    print(label_encoder.classes_)
    # for class_label, encoded_value in zip(label_encoder.classes_, label_encoder.transform(label_encoder.classes_)):
    #     print(f"{class_label}: {encoded_value}")
    return X, y_encoded, 2


def load_mali_data(csv_path):
    # 读取CSV文件
    try:
        df = pd.read_csv(csv_path)
    except Exception as e:
        print(f"Error loading data: {e}")
        return None, None, None

    # 提取特征和标签
    X = df.values
    num_samples = len(df)
    y = np.ones(num_samples)
    return X, y


def prepare_data(X_train, y_train):
    # 将数据转换为PyTorch张量并移至GPU
    dataset_train = TensorDataset(torch.tensor(X_train, dtype=torch.float32), torch.tensor(y_train, dtype=torch.long))
    dataloader_train = DataLoader(dataset_train, batch_size=BATCH_SIZE, shuffle=True)
    return dataloader_train


class CustomModel(nn.Module):
    def __init__(self, input_size, num_classes):
        super(CustomModel, self).__init__()
        self.conv1d_1 = nn.Conv1d(1, 64, kernel_size=3)
        self.relu = nn.ReLU()
        self.max_pool1d = nn.MaxPool1d(kernel_size=5, stride=4)
        self.batch_norm1 = nn.BatchNorm1d(num_features=64)
        self.conv1d_2 = nn.Conv1d(64, 1, kernel_size=3, padding=1)  # 1*20
        self.bidir_lstm1 = nn.LSTM(input_size=20, hidden_size=64, bidirectional=True, batch_first=True)
        self.max_pool1d_2 = nn.MaxPool1d(kernel_size=5, stride=5)
        self.batch_norm2 = nn.BatchNorm1d(num_features=1)
        self.bidir_lstm2 = nn.LSTM(input_size=25, hidden_size=128, bidirectional=True, batch_first=True)
        self.dropout = nn.Dropout(p=0.5)
        self.dense = nn.Linear(in_features=256, out_features=num_classes)
        self.softmax = nn.Softmax(dim=1)

    def forward(self, x):
        # pdb.set_trace()
        x = x.unsqueeze(1)
        x = self.conv1d_1(x)
        x = self.relu(x)
        x = self.max_pool1d(x)
        x = self.batch_norm1(x)
        x = self.conv1d_2(x)
        x, _ = self.bidir_lstm1(x)
        x = self.max_pool1d_2(x)
        x = self.batch_norm2(x)
        x, _ = self.bidir_lstm2(x)
        x = self.dropout(x)
        x = self.dense(x)
        x = x.squeeze(1)
        return x


def train_model(dataloader_train, device, input_size, class_size):
    logging.info("Starting the training...")

    model = CustomModel(input_size, class_size).to(device)
    # 定义损失函数和优化器
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.parameters(), lr=LEARNING_RATE)

    for epoch in range(NUM_EPOCHS):
        model.train()
        epoch_loss = 0.0
        for inputs, labels in dataloader_train:
            inputs, labels = inputs.to(device), labels.to(device)
            outputs = model(inputs)
            loss = criterion(outputs, labels)
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            epoch_loss += loss.item()
        avg_epoch_loss = epoch_loss / len(dataloader_train)
        logging.info(f"Epoch {epoch + 1}/{NUM_EPOCHS} - Loss: {avg_epoch_loss:.4f}")
    return model


def pgd_attack(model, inputs, labels, loss_fn=None, epsilon=0.04, alpha=0.005, num_steps=40):#epsilon=0.01, alpha=0.005
    # 使用PGD算法生成对抗性样本
    ori_inputs = deepcopy(inputs)
    targets = torch.zeros_like(labels).to(device)
    model.to(device)
    
    model.train()
    inputs.requires_grad = True
    for para in model.parameters():
        para.requires_grad = False
    optimizer = torch.optim.Adam([inputs])
    for _ in range(num_steps):
        # 前向传播计算损失
        optimizer.zero_grad()
        outputs = model(inputs)
        
        loss = loss_fn(outputs, targets)
        loss.backward()
        
        perturbed_inputs = (inputs - alpha * inputs.grad.sign()).detach_()
        
        # 将扰动限制在 epsilon 范围内
        eta = torch.clamp(perturbed_inputs- ori_inputs , min=-epsilon, max=epsilon)
        inputs = torch.clamp(ori_inputs + eta, min=0, max=1).detach_()
    model.eval()
    return inputs


def evaluate_model(model, X_test, y_test):
    model.eval()
    dataset_test = TensorDataset(torch.tensor(X_test, dtype=torch.float32), torch.tensor(y_test, dtype=torch.long))
    dataloader_test = DataLoader(dataset_test, batch_size=BATCH_SIZE, shuffle=False)
    y_true = []
    y_pred = []
    with torch.no_grad():
        for inputs, labels in dataloader_test:
            inputs, labels = inputs.to(DEVICE), labels.to(DEVICE)
            outputs = model(inputs)
            _, predicted = torch.max(outputs.data, 1)
            y_true.extend(labels.cpu().numpy())
            y_pred.extend(predicted.cpu().numpy())

    accuracy = accuracy_score(y_true, y_pred)
    precision = precision_score(y_true, y_pred, average='macro')
    recall = recall_score(y_true, y_pred, average='macro')
    f1 = f1_score(y_true, y_pred, average='macro')
    return accuracy, precision, recall, f1


def Advtrain(model,train_loader,epoch):
    model.train()
    running_loss = 0.0
    correct = 0
    total = 0
    criterion =  nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.parameters(), lr=0.001)
    logging.info(f'begin Advtrain Epoch {epoch + 1}')
    
    model_for_attack = deepcopy(model)
    model_for_attack.to(device)
    model_for_attack.eval()
    
    model.train()
    for inputs, labels in train_loader:
        inputs, labels = inputs.to(DEVICE), labels.to(DEVICE)
        if labels.data == 1:
            PGD_inputs = pgd_attack(model_for_attack,inputs,labels,loss_fn=criterion)
            inputs = torch.cat([inputs, PGD_inputs], dim=0)
            labels = torch.cat([labels,labels]) 

        optimizer.zero_grad()

        outputs = model(inputs)
        loss = criterion(outputs, labels)
        loss.backward()
        optimizer.step()

        running_loss += loss.item()
        # pdb.set_trace()
        _, predicted = outputs.max(1)
        total += labels.size(0)
        correct += predicted.eq(labels).sum().item()

    train_loss = running_loss / len(train_loader)
    train_acc = 100.0 * correct / total
    logging.info(f'Epoch {epoch + 1}/{num_epochs}, Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.2f}%')


def main():
    X, y, class_size = load_data()
    if X is not None:
        results = {'accuracy': [], 'precision': [], 'recall': [], 'f1': []}

        logging.info("Starting the experiment...")

        # Remove the k_values list and the loop
        results = {'accuracy': [], 'precision': [], 'recall': [], 'f1': []}


        # Directly split the data into training and testing sets
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=RANDOM_STATE)

        dataloader_train = prepare_data(X_train, y_train)
        model = train_model(dataloader_train, DEVICE, len(X_train[0]), class_size)
        accuracy, precision, recall, f1 = evaluate_model(model, X_test, y_test)

        # Append the single evaluation result to the results dictionary
        results['accuracy'].append(accuracy)
        results['precision'].append(precision)
        results['recall'].append(recall)
        results['f1'].append(f1)

        print(f"Accuracy: {accuracy:.4f}, Precision: {precision:.4f}, Recall: {recall:.4f}, F1: {f1:.4f}")
        logging.info(f"Accuracy: {accuracy:.4f}, Precision: {precision:.4f}, Recall: {recall:.4f}, F1: {f1:.4f}")

        model.eval()
        ori_correct = 0
        correct = 0
        total = 0
        scores, labels = [], []
        dataset_test = TensorDataset(torch.tensor(X_test, dtype=torch.float32), torch.tensor(y_test, dtype=torch.long))
        test_loader = DataLoader(dataset_test, batch_size=BATCH_SIZE, shuffle=False)
        for inputs, targets in test_loader:
            inputs, targets = inputs.to(DEVICE), targets.to(DEVICE)
            ori_outputs = model(inputs)
            _, ori_predicted = ori_outputs.max(1)
            ori_correct += ori_predicted.eq(targets).sum().item()
            criterion = nn.CrossEntropyLoss()
            PGD_inputs = pgd_attack(model,inputs,targets,loss_fn=criterion)
            outputs = model(PGD_inputs)
            _, predicted = outputs.max(1)
            total += targets.size(0)
            correct += predicted.eq(targets).sum().item()
            # pdb.set_trace()
            # num_zeros = targets.numel() - torch.count_nonzero(targets)
            # total += num_zeros
            # correct += torch.count_nonzero((predicted == 0) & (targets == 0)).item()
        test_acc = 100.0 * ori_correct / total
        logging.info(f"Original Accuracy: {test_acc:.4f}")

        adv_test_acc = 100.0 * correct / total
        logging.info(f"Adversarial Accuracy: {adv_test_acc:.4f}")


def adv_test(model,test_loader,plot_dir=None):
    model.eval()
    ori_correct = 0
    correct = 0
    total = 0
    scores, labels = [], []

    for inputs, targets in test_loader:
        inputs, targets = inputs.to(device), targets.to(device)
        ori_outputs = model(inputs)
        _, ori_predicted = ori_outputs.max(1)
        ori_correct += ori_predicted.eq(targets).sum().item()

        PGD_inputs = pgd_attack(model,inputs,targets,loss_fn=criterion)
        outputs = model(PGD_inputs)
        _, predicted = outputs.max(1)
        total += targets.size(0)
        correct += predicted.eq(targets).sum().item()
        # pdb.set_trace()
        # num_zeros = targets.numel() - torch.count_nonzero(targets)
        # total += num_zeros
        # correct += torch.count_nonzero((predicted == 0) & (targets == 0)).item()
    test_acc = 100.0 * ori_correct / total
    adv_test_acc = 100.0 * correct / total
    logging.info(f"Original Accuracy: {test_acc:.4f}")
    logging.info(f"Adversarial Accuracy: {adv_test_acc:.4f}")
    if plot_dir:
        if not os.path.exists(plot_dir):
            os.makedirs(plot_dir)
        # 绘制条形图并保存
        plt.figure(figsize=(10, 6))  # 可选，设置图形大小
        plt.bar(['Original', 'Adversarial'], [test_acc, adv_test_acc])
        plt.xlabel('Accuracy Type')
        plt.ylabel('Accuracy (%)')
        plt.title('Accuracy Comparison')
        plt.savefig(os.path.join(plot_dir, 'accuracy_comparison.png'))
        plt.close()  # 清除当前图像，避免重叠

        # 绘制饼图并保存
        plt.figure(figsize=(8, 8))  # 可选，根据需要调整图形大小
        accuracy_distribution = [test_acc, adv_test_acc]
        plt.pie(accuracy_distribution, labels=['Original', 'Adversarial'], autopct='%1.1f%%')
        plt.title('Accuracy Distribution')
        plt.savefig(os.path.join(plot_dir, 'accuracy_distribution.png'))
        plt.close()  # 清除当前图像，避免重叠

        # 计算混淆矩阵并保存
        plt.figure(figsize=(8, 8))  # 根据混淆矩阵大小调整
        cm = confusion_matrix(targets.cpu().numpy(), predicted.cpu().numpy())
        plt.imshow(cm, cmap='Blues', interpolation='nearest')
        plt.colorbar()
        classes = ['Benign', "Attack"]
        plt.xticks(range(len(classes)), classes)
        plt.yticks(range(len(classes)), classes)
        plt.xlabel('Predicted Class')
        plt.ylabel('True Class')
        plt.title('Confusion Matrix')
        plt.savefig(os.path.join(plot_dir, 'confusion_matrix.png'))
        plt.close()  # 清除当前图像，避免重叠
        logging.info("Saving plots...")
        


def adv_test_multi_kitsune(CNNmodel ,test_dataloader, kitsune_model_checkpoint=False, plot_dir=None):
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    CNNmodel.eval()
    ori_correct = 0
    correct = 0
    total = 0
    scores, labels = [], []
    y_true = []
    y_pred_ori = []
    y_pred = []

    if kitsune_model_checkpoint:
        logging.info("Loading kitsune...")
        Kitsune_model, threshold = Kitsune.from_pretrained(kitsune_model_checkpoint)
        Kitsune_model.to(device)
        Kitsune_model.eval()

    # pdb.set_trace()
    model_for_attack = deepcopy(CNNmodel)
    model_for_attack.to(device)
    model_for_attack.eval()

    for inputs, targets in test_dataloader:
        inputs, targets = inputs.to(device), targets.to(device)
        CNNmodel.eval()
        ori_outputs = CNNmodel(inputs)
        _, ori_predicted = torch.max(ori_outputs.data, 1)
        ori_correct += ori_predicted.eq(targets).sum().item()
        y_true.extend(targets.cpu().numpy())
        y_pred_ori.extend(ori_predicted.cpu().numpy())
        
        PGD_inputs = pgd_attack(model_for_attack, inputs,targets,loss_fn=criterion)
        CNNmodel.eval()
        outputs = CNNmodel(PGD_inputs)
        _, predicted = torch.max(outputs.data, 1)

        if kitsune_model_checkpoint:
            scores = Kitsune_model.score(PGD_inputs)
            kit_scores = (scores > threshold).type(torch.long)
            predicted = torch.where(kit_scores == 1, torch.tensor(1, dtype=torch.long), predicted)
        y_pred.extend(predicted.cpu().numpy())
    test_acc = accuracy_score(y_true, y_pred_ori)
    precision = precision_score(y_true, y_pred_ori, average='macro')
    recall = recall_score(y_true, y_pred_ori, average='macro')  
    f1 = f1_score(y_true, y_pred_ori, average='macro')

    adv_test_acc = accuracy_score(y_true, y_pred)
    precision_adv = precision_score(y_true, y_pred, average='macro')
    recall_adv = recall_score(y_true, y_pred, average='macro')
    f1_adv = f1_score(y_true, y_pred, average='macro')
    logging.info(f"NoADV.INFO | Accuracy_: {test_acc:.4f}, Precision: {precision:.4f}, Recall: {recall:.4f}, F1: {f1:.4f}")
    logging.info(f"ADV.INFO | Accuracy_: {adv_test_acc:.4f}, Precision: {precision_adv:.4f}, Recall: {recall_adv:.4f}, F1: {f1_adv:.4f}")

    if plot_dir:
        if not os.path.exists(plot_dir):
            os.makedirs(plot_dir)
        # 绘制条形图并保存
        plt.bar(['Original', 'Adversarial'], [test_acc, adv_test_acc])
        plt.xlabel('Accuracy Type')
        plt.ylabel('Accuracy (%)')
        plt.title('Accuracy Comparison')
        plt.savefig(os.path.join(plot_dir, 'accuracy_comparison.png'))
        plt.close()  # 清除当前图像，避免重叠

        # 绘制饼图并保存
        accuracy_distribution = [test_acc, adv_test_acc]
        plt.pie(accuracy_distribution, labels=['Original', 'Adversarial'], autopct='%1.1f%%')
        plt.title('Accuracy Distribution')
        plt.savefig(os.path.join(plot_dir, 'accuracy_distribution.png'))
        plt.close()  # 清除当前图像，避免重叠

        # 计算混淆矩阵并保存
        cm = confusion_matrix(targets.cpu().numpy(), predicted.cpu().numpy())
        plt.imshow(cm, cmap='Blues', interpolation='nearest')
        plt.colorbar()
        classes = ['Benign', "Attack"]
        plt.xticks(range(len(classes)), classes)
        plt.yticks(range(len(classes)), classes)
        plt.xlabel('Predicted Class')
        plt.ylabel('True Class')
        plt.title('Confusion Matrix')
        plt.savefig(os.path.join(plot_dir, 'confusion_matrix.png'))
        plt.close()  # 清除当前图像，避免重叠
        logging.info("Saving plots...")
    

num_epochs = 6
X, y, class_size = load_data("Data/CIC/benignandmali_data_withlabels_normalized.csv")  # label:0,1、normalized

X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, stratify=y,random_state=RANDOM_STATE)

dataloader_train = prepare_data(X_train, y_train)
dataset_test = TensorDataset(torch.tensor(X_test, dtype=torch.float32), torch.tensor(y_test, dtype=torch.long))

ben_indices = [i for i, (_, label) in enumerate(dataset_test) if label == 0]
benign_test_dataset = Subset(dataset_test, ben_indices)
mal_indices = [i for i, (_, label) in enumerate(dataset_test) if label == 1]
mali_test_dataset = Subset(dataset_test, mal_indices)

benign_test_loader = DataLoader(benign_test_dataset, batch_size=BATCH_SIZE, shuffle=False)
mali_test_loader = DataLoader(mali_test_dataset, batch_size=BATCH_SIZE, shuffle=False)
print('特征长度:', len(X_train[0]))

model = CustomModel(len(X_train[0]), class_size).to(DEVICE)
# model.load_state_dict(torch.load('Models/CIC-CNN-BiLSTM-known.pth'))
criterion = nn.CrossEntropyLoss()
optimizer = optim.Adam(model.parameters(), lr=0.001)

if os.path.exists(detect_bin_model_path):
    model.load_state_dict(torch.load(detect_bin_model_path))
    model.to(device)
else:
    #  训练分类模型
    model.train()
    for epoch in range(NUM_EPOCHS):        
        epoch_loss = 0.0
        for inputs, labels in dataloader_train:
            inputs, labels = inputs.to(device), labels.to(device)
            outputs = model(inputs)
            loss = criterion(outputs, labels)
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            epoch_loss += loss.item()
        avg_epoch_loss = epoch_loss / len(dataloader_train)
        logging.info(f"Epoch {epoch + 1}/{NUM_EPOCHS} - Loss: {avg_epoch_loss:.4f}")
    torch.save(model.state_dict(), detect_bin_model_path)
logging.info("detect_bin_model is ready")

model.eval()

# y_true = []
# y_pred = []
# # pdb.set_trace()
# with torch.no_grad():
#     for inputs, labels in dataloader_test:
#         inputs, labels = inputs.to(DEVICE), labels.to(DEVICE)
#         outputs = model(inputs)
#         _, predicted = torch.max(outputs.data, 1)
#         y_true.extend(labels.cpu().numpy())
#         y_pred.extend(predicted.cpu().numpy())

# accuracy = accuracy_score(y_true, y_pred)
# precision = precision_score(y_true, y_pred, average='macro')
# recall = recall_score(y_true, y_pred, average='macro')
# f1 = f1_score(y_true, y_pred, average='macro')
# logging.info(f"Accuracy: {accuracy:.4f}, Precision: {precision:.4f}, Recall: {recall:.4f}, F1: {f1:.4f}")


# logging.info("No advtrain | benign_data | CustomModel ")
# adv_test_multi_kitsune(model, benign_test_loader)
logging.info("No advtrain | mali_data | CustomModel ")
adv_test_multi_kitsune(model, mali_test_loader)

# logging.info("No advtrain | benign_data | CustomModel&kitsune ")
# adv_test_multi_kitsune(model, benign_test_loader, "Models/kitsune_CIC.pt")
logging.info("No advtrain | mali_data | CustomModel&kitsune ")
adv_test_multi_kitsune(model, mali_test_loader, "Models/kitsune_CIC.pt")

logging.info("Advtrain begin")
# adv_test(model, test_loader)
for epoch in range(num_epochs):    
    Advtrain(model, dataloader_train, epoch)
    logging.info("advtrain | mali_data | CustomModel ")
    # adv_test_multi_kitsune(model, test_loader, plot_dir="Plots/Advdefense-CIC-CNN-Bilstm_kitnet-bin/adv_to_test_model")
    # logging.info("advtrain :::mali_dataloader_test test model")
    adv_test_multi_kitsune(model, mali_test_loader)

    logging.info("advtrain | mali_data | CustomModel&kitsune")
    # adv_test_multi_kitsune(model, test_loader, kitsune_model_checkpoint="Models/kitsune_CIC.pt", plot_dir="Plots/Advdefense-CIC-CNN-Bilstm_kitnet-bin/adv_to_test_kitsune_model")
    # logging.info("advtrain :::mali_dataloader_test to test kitsune&model")
    adv_test_multi_kitsune(model, mali_test_loader, kitsune_model_checkpoint="Models/kitsune_CIC.pt")






