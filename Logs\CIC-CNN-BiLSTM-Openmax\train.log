2025-08-13 15:26:30,582 - INFO - Fittting <PERSON>bull distribution...
2025-08-13 15:26:33,065 - INFO - Evaluation...
2025-08-13 15:26:34,282 - INFO - ================================================================================
2025-08-13 15:26:34,282 - INFO - SOFTMAX RESULTS
2025-08-13 15:26:34,282 - INFO - ================================================================================
2025-08-13 15:26:34,282 - INFO - Softmax accuracy is 0.487
2025-08-13 15:26:34,282 - INFO - Softmax precision is 0.297
2025-08-13 15:26:34,282 - INFO - Softmax recall is 0.487
2025-08-13 15:26:34,282 - INFO - Softmax F1 is 0.487
2025-08-13 15:26:34,282 - INFO - Softmax f1_macro is 0.618
2025-08-13 15:26:34,282 - INFO - Softmax f1_macro_weighted is 0.351
2025-08-13 15:26:34,282 - INFO - Softmax area_under_roc is 0.995
2025-08-13 15:26:34,282 - INFO - Softmax Confusion Matrix Metrics:
2025-08-13 15:26:34,282 - INFO - Average TP: 642.14
2025-08-13 15:26:34,282 - INFO - Average FP: 676.29
2025-08-13 15:26:34,282 - INFO - Average TN: 7234.29
2025-08-13 15:26:34,282 - INFO - Average FN: 676.29
2025-08-13 15:26:34,282 - INFO - Total TP: 4495
2025-08-13 15:26:34,282 - INFO - Total FP: 4734
2025-08-13 15:26:34,282 - INFO - Total TN: 50640
2025-08-13 15:26:34,282 - INFO - Total FN: 4734
2025-08-13 15:26:34,282 - INFO - Softmax classification_report:
{'0': {'precision': 0.3578005115089514, 'recall': 0.9992857142857143, 'f1-score': 0.5269303201506591, 'support': 1400.0}, '1': {'precision': 0.8663366336633663, 'recall': 1.0, 'f1-score': 0.9283819628647215, 'support': 1400.0}, '2': {'precision': 0.36430138990490124, 'recall': 0.996, 'f1-score': 0.533476164970541, 'support': 500.0}, '3': {'precision': 1.0, 'recall': 1.0, 'f1-score': 1.0, 'support': 450.0}, '4': {'precision': 0.2716723549488055, 'recall': 0.9974937343358395, 'f1-score': 0.42703862660944203, 'support': 399.0}, '5': {'precision': 0.831353919239905, 'recall': 1.0, 'f1-score': 0.9079118028534371, 'support': 350.0}, '6': {'precision': 0.0, 'recall': 0.0, 'f1-score': 0.0, 'support': 4730.0}, 'accuracy': 0.4870516849062737, 'macro avg': {'precision': 0.5273521156094185, 'recall': 0.8561113498030791, 'f1-score': 0.6176769824926859, 'support': 9229.0}, 'weighted avg': {'precision': 0.29746601360409963, 'recall': 0.4870516849062737, 'f1-score': 0.3513200586978734, 'support': 9229.0}}
2025-08-13 15:26:34,282 - INFO - ================================================================================
2025-08-13 15:26:34,282 - INFO - SOFTMAX THRESHOLD RESULTS
2025-08-13 15:26:34,282 - INFO - ================================================================================
2025-08-13 15:26:34,282 - INFO - SoftmaxThreshold accuracy is 0.658
2025-08-13 15:26:34,282 - INFO - SoftmaxThreshold precision is 0.851
2025-08-13 15:26:34,282 - INFO - SoftmaxThreshold recall is 0.658
2025-08-13 15:26:34,282 - INFO - SoftmaxThreshold F1 is 0.658
2025-08-13 15:26:34,282 - INFO - SoftmaxThreshold f1_macro is 0.781
2025-08-13 15:26:34,282 - INFO - SoftmaxThreshold f1_macro_weighted is 0.641
2025-08-13 15:26:34,282 - INFO - SoftmaxThreshold area_under_roc is 0.995
2025-08-13 15:26:34,282 - INFO - SoftmaxThreshold Confusion Matrix Metrics:
2025-08-13 15:26:34,282 - INFO - Average TP: 867.43
2025-08-13 15:26:34,282 - INFO - Average FP: 451.00
2025-08-13 15:26:34,282 - INFO - Average TN: 7459.57
2025-08-13 15:26:34,282 - INFO - Average FN: 451.00
2025-08-13 15:26:34,282 - INFO - Total TP: 6072
2025-08-13 15:26:34,282 - INFO - Total FP: 3157
2025-08-13 15:26:34,282 - INFO - Total TN: 52217
2025-08-13 15:26:34,282 - INFO - Total FN: 3157
2025-08-13 15:26:34,282 - INFO - SoftmaxThreshold classification_report:
{'0': {'precision': 0.3832876712328767, 'recall': 0.9992857142857143, 'f1-score': 0.554059405940594, 'support': 1400.0}, '1': {'precision': 0.9079118028534371, 'recall': 1.0, 'f1-score': 0.9517335146159076, 'support': 1400.0}, '2': {'precision': 0.4353146853146853, 'recall': 0.996, 'f1-score': 0.6058394160583941, 'support': 500.0}, '3': {'precision': 1.0, 'recall': 1.0, 'f1-score': 1.0, 'support': 450.0}, '4': {'precision': 0.8614718614718615, 'recall': 0.9974937343358395, 'f1-score': 0.9245063879210221, 'support': 399.0}, '5': {'precision': 0.8684863523573201, 'recall': 1.0, 'f1-score': 0.9296148738379814, 'support': 350.0}, '6': {'precision': 0.9993662864385298, 'recall': 0.33340380549682874, 'f1-score': 0.5, 'support': 4730.0}, 'accuracy': 0.6579261025029798, 'macro avg': {'precision': 0.7794055228098158, 'recall': 0.9037404648740547, 'f1-score': 0.7808219426248427, 'support': 9229.0}, 'weighted avg': {'precision': 0.8505836642414955, 'recall': 0.6579261025029798, 'f1-score': 0.6414858653626699, 'support': 9229.0}}
2025-08-13 15:26:34,287 - INFO - ================================================================================
2025-08-13 15:26:34,287 - INFO - OPENMAX RESULTS
2025-08-13 15:26:34,287 - INFO - ================================================================================
2025-08-13 15:26:34,287 - INFO - OpenMax accuracy is 0.849
2025-08-13 15:26:34,287 - INFO - OpenMax precision is 0.871
2025-08-13 15:26:34,287 - INFO - OpenMax recall is 0.849
2025-08-13 15:26:34,287 - INFO - OpenMax F1 is 0.849
2025-08-13 15:26:34,287 - INFO - OpenMax f1_macro is 0.881
2025-08-13 15:26:34,287 - INFO - OpenMax f1_macro_weighted is 0.855
2025-08-13 15:26:34,287 - INFO - OpenMax area_under_roc is 0.954
2025-08-13 15:26:34,287 - INFO - OpenMax Confusion Matrix Metrics:
2025-08-13 15:26:34,287 - INFO - Average TP: 1119.43
2025-08-13 15:26:34,287 - INFO - Average FP: 199.00
2025-08-13 15:26:34,287 - INFO - Average TN: 7711.57
2025-08-13 15:26:34,287 - INFO - Average FN: 199.00
2025-08-13 15:26:34,287 - INFO - Total TP: 7836
2025-08-13 15:26:34,287 - INFO - Total FP: 1393
2025-08-13 15:26:34,287 - INFO - Total TN: 53981
2025-08-13 15:26:34,287 - INFO - Total FN: 1393
2025-08-13 15:26:34,287 - INFO - OpenMax classification_report:
{'0': {'precision': 0.5985808413583376, 'recall': 0.8435714285714285, 'f1-score': 0.7002668247850578, 'support': 1400.0}, '1': {'precision': 1.0, 'recall': 0.9142857142857143, 'f1-score': 0.955223880597015, 'support': 1400.0}, '2': {'precision': 1.0, 'recall': 0.732, 'f1-score': 0.8452655889145496, 'support': 500.0}, '3': {'precision': 1.0, 'recall': 0.9911111111111112, 'f1-score': 0.9955357142857144, 'support': 450.0}, '4': {'precision': 0.9967320261437909, 'recall': 0.7644110275689223, 'f1-score': 0.8652482269503546, 'support': 399.0}, '5': {'precision': 1.0, 'recall': 0.9142857142857143, 'f1-score': 0.955223880597015, 'support': 350.0}, '6': {'precision': 0.8677831643895989, 'recall': 0.8325581395348837, 'f1-score': 0.8498057833405265, 'support': 4730.0}, 'accuracy': 0.8490627370245963, 'macro avg': {'precision': 0.9232994331273897, 'recall': 0.8560318764796822, 'f1-score': 0.880938557067176, 'support': 9229.0}, 'weighted avg': {'precision': 0.8712020396463157, 'recall': 0.8490627370245963, 'f1-score': 0.8546380549770923, 'support': 9229.0}}
2025-08-13 15:26:34,287 - INFO - ================================================================================
2025-08-13 15:26:34,287 - INFO - 
COMPREHENSIVE COMPARISON TABLE
2025-08-13 15:26:34,287 - INFO - ========================================================================================================================
2025-08-13 15:26:34,287 - INFO - Method               Accuracy   Precision  Recall     F1         F1_Macro   AUC        TP       FP       TN       FN      
2025-08-13 15:26:34,287 - INFO - ------------------------------------------------------------------------------------------------------------------------
2025-08-13 15:26:34,287 - INFO - Softmax              0.487      0.297      0.487      0.487      0.618      0.995      642.14   676.29   7234.29  676.29  
2025-08-13 15:26:34,287 - INFO - SoftmaxThreshold     0.658      0.851      0.658      0.658      0.781      0.995      867.43   451.00   7459.57  451.00  
2025-08-13 15:26:34,289 - INFO - OpenMax              0.849      0.871      0.849      0.849      0.881      0.954      1119.43  199.00   7711.57  199.00  
2025-08-13 15:26:34,289 - INFO - ========================================================================================================================
