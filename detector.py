'''全连接网络分类器'''
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from torch.utils.data import TensorDataset, DataLoader,Dataset
import matplotlib.pyplot as plt
import pdb
import pickle as pkl
import pandas as pd

CLASS_DICT = {
    'dos_hulk': 0,
    'portscan': 1,
    'ftp_patator': 2,
    'dos_slowloris': 3,
    'dos_slowhttptest': 4,
    'webattack_bruteforce': 5
}
class CustomDataset(Dataset):
    def __init__(self, csv_file):
        self.data = pd.read_csv(csv_file)
        self.features = self.data.iloc[:, :75].values
        self.labels = self.data.iloc[:, -1].values

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        features = torch.tensor(self.features[idx], dtype=torch.float32)
        
        label = torch.tensor(CLASS_DICT[self.labels[idx]], dtype=torch.long)

        return features, label

class Classifier(nn.Module):
    def __init__(self, feature_size):
        super(Classifier, self).__init__()
        self.fc1 = nn.Linear(feature_size, 256)
        self.dropout1 = nn.Dropout(0.5)
        self.fc2 = nn.Linear(256, 128)
        self.dropout2 = nn.Dropout(0.5)
        self.fc3 = nn.Linear(128, 64)
        self.dropout3 = nn.Dropout(0.5)
        self.fc4 = nn.Linear(64, 32)
        self.dropout4 = nn.Dropout(0.5)
        self.fc5 = nn.Linear(32, 6)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        x = self.dropout1(self.sigmoid(self.fc1(x)))
        x = self.dropout2(self.sigmoid(self.fc2(x)))
        x = self.dropout3(self.sigmoid(self.fc3(x)))
        x = self.dropout4(self.sigmoid(self.fc4(x)))
        x = self.fc5(x)
        return x


# 准备数据
train_dataset = CustomDataset(r'Data\train_data.csv')
test_dataset = CustomDataset(r'Data\test_data.csv')

train_loader = DataLoader(train_dataset, batch_size=128, shuffle=True)
test_loader = DataLoader(test_dataset, batch_size=128, shuffle=False)

# 定义模型、损失函数和优化器
model = Classifier(feature_size=75)
criterion = nn.CrossEntropyLoss()
optimizer = optim.Adam(model.parameters(), lr=0.001)

# 训练模型
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
model.to(device)

num_epochs = 100
for epoch in range(num_epochs):
    model.train()
    running_loss = 0.0
    correct = 0
    total = 0

    for inputs, labels in train_loader:
        inputs, labels = inputs.to(device), labels.to(device)
        
        optimizer.zero_grad()

        outputs = model(inputs)
        loss = criterion(outputs, labels)
        loss.backward()
        optimizer.step()

        running_loss += loss.item()
        # pdb.set_trace()
        _, predicted = outputs.max(1)
        total += labels.size(0)
        correct += predicted.eq(labels).sum().item()

    train_loss = running_loss / len(train_loader)
    train_acc = 100.0 * correct / total

    print(f'Epoch {epoch+1}/{num_epochs}, Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.2f}%')
torch.save(model.state_dict(), 'Weight/DNN_weights.pth')

# 评估模型
model.eval()
correct = 0
total = 0

with torch.no_grad():
    for inputs, labels in test_loader:
        inputs, labels = inputs.to(device), labels.to(device)

        outputs = model(inputs)
        _, predicted = outputs.max(1)
        total += labels.size(0)
        correct += predicted.eq(labels).sum().item()

test_acc = 100.0 * correct / total
print(f'Test Acc: {test_acc:.2f}%')