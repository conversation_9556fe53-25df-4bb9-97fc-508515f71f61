import torch
import torch.nn as nn
import torch.optim as optim
import torch.utils.data
from sklearn.model_selection import train_test_split, KFold, StratifiedKFold
from sklearn.preprocessing import LabelEncoder
from torch.utils.data import TensorDataset, DataLoader
import pandas as pd
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
import matplotlib.pyplot as plt
import logging

import pdb

# 定义全局常量
BATCH_SIZE = 1024
LEARNING_RATE = 0.001
NUM_EPOCHS = 10
RANDOM_STATE = 42
DEVICE = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
logging.basicConfig(filename=r'Logs\CIC-CNN-BiLSTM-known/log.log', level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


def load_data():
    # 读取CSV文件
    try:
        df = pd.read_csv('Data/CIC_orig/benignandmali_data_withlabels.csv', header=None)
    except Exception as e:
        print(f"Error loading data: {e}")
        return None, None, None

    # 提取特征和标签
    X = df.iloc[:, :-1].values
    y = df.iloc[:, -1].values

    # 将标签进行数字编码
    label_encoder = LabelEncoder()
    y_encoded = label_encoder.fit_transform(y)

    return X, y_encoded, len(label_encoder.classes_)

def prepare_data(X_train, y_train):
    # 将数据转换为PyTorch张量并移至GPU
    dataset_train = TensorDataset(torch.tensor(X_train, dtype=torch.float32), torch.tensor(y_train, dtype=torch.long))
    dataloader_train = DataLoader(dataset_train, batch_size=BATCH_SIZE, shuffle=True)
    return dataloader_train

class CustomModel(nn.Module):
    def __init__(self, input_size, num_classes):
        super(CustomModel, self).__init__()
        self.conv1d_1 = nn.Conv1d(1, 64, kernel_size=3, padding=1)
        self.relu = nn.ReLU()
        self.max_pool1d = nn.MaxPool1d(kernel_size=5, stride=5)
        self.batch_norm1 = nn.BatchNorm1d(num_features=64)
        self.conv1d_2 = nn.Conv1d(64, 1, kernel_size=3, padding=1)
        self.bidir_lstm1 = nn.LSTM(input_size=15, hidden_size=64, bidirectional=True, batch_first=True)
        self.max_pool1d_2 = nn.MaxPool1d(kernel_size=5, stride=5)
        self.batch_norm2 = nn.BatchNorm1d(num_features=1)
        self.bidir_lstm2 = nn.LSTM(input_size=25, hidden_size=128, bidirectional=True, batch_first=True)
        self.dropout = nn.Dropout(p=0.5)
        self.dense = nn.Linear(in_features=256, out_features=num_classes)
        self.softmax = nn.Softmax(dim=1)

    def forward(self, x):
        # pdb.set_trace()
        x = x.unsqueeze(1)
        x = self.conv1d_1(x)
        x = self.relu(x)
        x = self.max_pool1d(x)
        x = self.batch_norm1(x)
        x = self.conv1d_2(x)
        x, _ = self.bidir_lstm1(x)
        x = self.max_pool1d_2(x)
        x = self.batch_norm2(x)
        x, _ = self.bidir_lstm2(x)
        x = self.dropout(x)
        x = self.dense(x)
        x = x.squeeze(1)
        return x

def train_model(dataloader_train, device, input_size, class_size):
    # 定义MLP模型
    model = CustomModel(input_size, class_size).to(device)
    # 定义损失函数和优化器
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.parameters(), lr=LEARNING_RATE)

    for epoch in range(NUM_EPOCHS):
        model.train()
        epoch_loss = 0.0
        for inputs, labels in dataloader_train:
            inputs, labels = inputs.to(device), labels.to(device)
            outputs = model(inputs)
            loss = criterion(outputs, labels)
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            epoch_loss += loss.item()
        avg_epoch_loss = epoch_loss / len(dataloader_train)
        logging.info(f"Epoch {epoch + 1}/{NUM_EPOCHS} - Loss: {avg_epoch_loss:.4f}")
    return model

def evaluate_model(model, X_test, y_test):
    model.eval()
    dataset_test = TensorDataset(torch.tensor(X_test, dtype=torch.float32), torch.tensor(y_test, dtype=torch.long))
    dataloader_test = DataLoader(dataset_test, batch_size=BATCH_SIZE, shuffle=False)
    y_true = []
    y_pred = []
    with torch.no_grad():
        for inputs, labels in dataloader_test:
            inputs, labels = inputs.to(DEVICE), labels.to(DEVICE)
            outputs = model(inputs)
            _, predicted = torch.max(outputs.data, 1)
            y_true.extend(labels.cpu().numpy())
            y_pred.extend(predicted.cpu().numpy())

    accuracy = accuracy_score(y_true, y_pred)
    precision = precision_score(y_true, y_pred, average='macro')
    recall = recall_score(y_true, y_pred, average='macro')
    f1 = f1_score(y_true, y_pred, average='macro')
    return accuracy, precision, recall, f1

def plot_metrics(k_values, metrics, metric_name, save_path=None):
    plt.figure(figsize=(10, 6))
    for metric, values in metrics.items():
        plt.plot(k_values, values, marker='o', label=metric)
    plt.title(f'{metric_name} for Different k Values in k-Fold Cross-Validation')
    plt.xlabel('k')
    plt.ylabel(metric_name)
    plt.legend()
    plt.grid(True)
    if save_path is not None:
        plt.savefig(save_path)  # 保存图像
    else:
        plt.show()  # 显示图像

def main():
    X, y, class_size = load_data()
    if X is not None:
        k_values = [2,4,6,8,10]#
        results = {'accuracy': [], 'precision': [], 'recall': [], 'f1': []}

        logging.info("Starting the experiment...")

        for k in k_values:
            print(f"Running {k}-Fold Cross-Validation")
            kf = KFold(n_splits=k, shuffle=True, random_state=RANDOM_STATE)
            kf = StratifiedKFold(n_splits=k, shuffle=True, random_state=RANDOM_STATE)
            fold_results = {'accuracy': [], 'precision': [], 'recall': [], 'f1': []}

            for fold, (train_index, test_index) in enumerate(kf.split(X,y)):
                print(f"Fold {fold + 1}/{k}")
                X_train, X_test = X[train_index], X[test_index]
                y_train, y_test = y[train_index], y[test_index]
                dataloader_train = prepare_data(X_train, y_train)
                model = train_model(dataloader_train, DEVICE, len(X_train[0]), class_size)
                accuracy, precision, recall, f1 = evaluate_model(model, X_test, y_test)
                fold_results['accuracy'].append(accuracy)
                fold_results['precision'].append(precision)
                fold_results['recall'].append(recall)
                fold_results['f1'].append(f1)
                print(f"Fold {fold + 1} - Accuracy: {accuracy:.4f}, Precision: {precision:.4f}, Recall: {recall:.4f}, F1: {f1:.4f}")
                logging.info(f"Fold {fold + 1}/{k}:")
                logging.info(f"Accuracy: {accuracy:.4f}, Precision: {precision:.4f}, Recall: {recall:.4f}, F1: {f1:.4f}")

            avg_accuracy = sum(fold_results['accuracy']) / k
            avg_precision = sum(fold_results['precision']) / k
            avg_recall = sum(fold_results['recall']) / k
            avg_f1 = sum(fold_results['f1']) / k

            results['accuracy'].append(avg_accuracy)
            results['precision'].append(avg_precision)
            results['recall'].append(avg_recall)
            results['f1'].append(avg_f1)

        plot_metrics(k_values, results, 'Accuracy', save_path=r'Plots/CIC-CNN-BiLSTM-known/accuracy_plot.png')
        plot_metrics(k_values, results, 'Precision', save_path=r'Plots/CIC-CNN-BiLSTM-known/precision_plot.png')
        plot_metrics(k_values, results, 'Recall', save_path=r'Plots/CIC-CNN-BiLSTM-known/recall_plot.png')
        plot_metrics(k_values, results, 'F1 Score', save_path=r'Plots/CIC-CNN-BiLSTM-known/f1_plot.png')
        torch.save(model.state_dict(), 'Models/CIC-CNN-BiLSTM-known.pth')
        print("Model saved successfully.")
        logging.info("Experiment completed.")
        logging.info("Model saved successfully.")

if __name__ == "__main__":
    main()




# import torch
# import torch.nn as nn
# import torch.optim as optim
# import torch.utils.data
# from sklearn.model_selection import train_test_split
# from sklearn.preprocessing import LabelEncoder
# from torch.utils.data import TensorDataset, DataLoader
# import pandas as pd
# from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score

# import pdb
# # 定义全局常量
# BATCH_SIZE = 1024
# LEARNING_RATE = 0.001
# NUM_EPOCHS = 10
# RANDOM_STATE = 42
# DEVICE = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

# def load_data():
#     # 读取CSV文件
#     try:
#         df = pd.read_csv('Data/CIC/benignandmali_data_withlabels.csv', header=None)
#     except Exception as e:
#         print(f"Error loading data: {e}")
#         return None, None, None, None

#     # 提取特征和标签
#     X = df.iloc[:, :-1].values
#     y = df.iloc[:, -1].values

#     # 将标签进行数字编码
#     label_encoder = LabelEncoder()
#     y_encoded = label_encoder.fit_transform(y)

#     # 将标签转换为PyTorch张量
#     y_tensor = torch.tensor(y_encoded, dtype=torch.long)

#     # 划分训练集和测试集
#     X_train, X_test, y_train, y_test = train_test_split(X, y_tensor, test_size=0.3, random_state=RANDOM_STATE)

#     return X_train, X_test, y_train, y_test,len(label_encoder.classes_)

# def prepare_data(X_train, X_test, y_train, y_test):
#     # 将数据转换为PyTorch张量并移至GPU
#     dataset_train = TensorDataset(torch.tensor(X_train, dtype=torch.float32), torch.tensor(y_train, dtype=torch.long))
#     dataset_test = TensorDataset(torch.tensor(X_test, dtype=torch.float32), torch.tensor(y_test, dtype=torch.long))
    
#     dataloader_train = DataLoader(dataset_train, batch_size=BATCH_SIZE, shuffle=True)
#     dataloader_test = DataLoader(dataset_test, batch_size=BATCH_SIZE, shuffle=False)
    
#     return dataloader_train, dataloader_test

# # class CustomModel(nn.Module):
# #     def __init__(self, input_size, hidden_size, num_classes):
# #         super(CustomModel, self).__init__()
# #         self.fc1 = nn.Linear(input_size, hidden_size)
# #         self.relu = nn.ReLU()
# #         self.fc2 = nn.Linear(hidden_size, num_classes)

# #     def forward(self, x):
# #         out = self.fc1(x)
# #         out = self.relu(out)
# #         out = self.fc2(out)
# #         return out


# class CustomModel(nn.Module):
#     def __init__(self, input_size, num_classes):
#         super(CustomModel, self).__init__()

#         # Convolutional layer
#         self.conv1d_1 = nn.Conv1d(1, 64, kernel_size=3, padding=1)
#         self.relu = nn.ReLU()

#         # Max pooling layer
#         self.max_pool1d = nn.MaxPool1d(kernel_size=5, stride=5)

#         # Batch normalization layer
#         self.batch_norm1 = nn.BatchNorm1d(num_features=64)

#         self.conv1d_2 = nn.Conv1d(64, 1, kernel_size=3, padding=1)

#         # Bidirectional LSTM layer
#         self.bidir_lstm1 = nn.LSTM(input_size=15, hidden_size=64, bidirectional=True, batch_first=True)

#         # Reshape layer (unnecessary in PyTorch since we can directly pass the output to the next layer)
#         # self.reshape = nn.Reshape((128, 1))

#         # Second max pooling layer
#         self.max_pool1d_2 = nn.MaxPool1d(kernel_size=5, stride=5)

#         # Second batch normalization layer
#         self.batch_norm2 = nn.BatchNorm1d(num_features=1)

#         # Second bidirectional LSTM layer
#         self.bidir_lstm2 = nn.LSTM(input_size=25, hidden_size=128, bidirectional=True, batch_first=True)

#         # Dropout layer
#         self.dropout = nn.Dropout(p=0.5)

#         # Dense layer
#         self.dense = nn.Linear(in_features=256, out_features=num_classes)

#         # Softmax activation function
#         self.softmax = nn.Softmax(dim=1)

#     def forward(self, x):
#         x = x.unsqueeze(1)
#         # print(x.shape)
#         x = self.conv1d_1(x)
#         # print(x.shape)
#         x = self.relu(x)
#         x = self.max_pool1d(x)
#         # print(x.shape)
#         x = self.batch_norm1(x)
#         x = self.conv1d_2(x)
#         # print(x.shape)
#         x, _ = self.bidir_lstm1(x)
#         # x = self.reshape(x)  # Not needed in PyTorch
#         # print(x.shape)
#         x = self.max_pool1d_2(x)
#         # print(x.shape)
#         x = self.batch_norm2(x)
#         # print(x.shape)
#         x, _ = self.bidir_lstm2(x)
#         # print(x.shape)
#         x = self.dropout(x)
#         x = self.dense(x)
#         x = x.squeeze(1)
#         # x = self.softmax(x)
#         # print(x[0])
#         return x



# def train_model(dataloader_train, dataloader_test, device,input_size,class_size):
#     # 定义MLP模型
#     model = CustomModel(input_size, class_size).to(device)

#     # 定义损失函数和优化器
#     criterion = nn.CrossEntropyLoss()
#     optimizer = optim.Adam(model.parameters(), lr=LEARNING_RATE)

#     for epoch in range(NUM_EPOCHS):
#         model.train()
#         epoch_loss = 0.0

#         for inputs, labels in dataloader_train:
#             inputs, labels = inputs.to(device), labels.to(device)
#             # pdb.set_trace()
#             # 前向传播
#             outputs = model(inputs)
#             loss = criterion(outputs, labels)

#             # 反向传播和优化
#             optimizer.zero_grad()
#             loss.backward()
#             optimizer.step()

#             epoch_loss += loss.item()

#         # 在测试集上进行预测
#         model.eval()
#         epoch_loss = 0
#         y_true = []
#         y_pred = []
#         with torch.no_grad():
#             correct = 0
#             total = 0
#             for inputs, labels in dataloader_test:
#                 inputs, labels = inputs.to(device), labels.to(device)
#                 outputs = model(inputs)
#                 _, predicted = torch.max(outputs.data, 1)
#                 total += labels.size(0)
#                 # pdb.set_trace()
#                 correct += (predicted == labels).sum().item()
#                 y_true.extend(labels.cpu().numpy())
#                 y_pred.extend(predicted.cpu().numpy())

#         accuracy = accuracy_score(y_true, y_pred)
#         precision = precision_score(y_true, y_pred, average='macro')
#         recall = recall_score(y_true, y_pred, average='macro')
#         f1 = f1_score(y_true, y_pred, average='macro')
#         print(f'Epoch {epoch+1}/{NUM_EPOCHS}, Accuracy: {accuracy:.4f}, Precision: {precision:.4f}, Recall: {recall:.4f}, F1: {f1:.4f}')

#         # accuracy = correct / total
#         # print(f'Epoch {epoch+1}/{NUM_EPOCHS}, Loss: {epoch_loss/len(dataloader_train):.4f}, Accuracy: {accuracy:.4f}')

#     return model

# def main():
#     X_train, X_test, y_train, y_test,class_size = load_data()
#     if X_train is not None:
#         dataloader_train, dataloader_test = prepare_data(X_train, X_test, y_train, y_test)
#         model = train_model(dataloader_train, dataloader_test, DEVICE,len(X_train[0]),class_size)
#         torch.save(model.state_dict(), 'Models/CIC-CNN-BiLSTM-known.pth')
#         print("Model saved successfully.")
# if __name__ == "__main__":
#     main()