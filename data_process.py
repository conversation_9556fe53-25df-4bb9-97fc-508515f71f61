import pandas as pd
import glob
from kitsune import train
import numpy as np


days_of_week = ['Monday_', 'Tuesday_', 'Wednesday_', 'Thursday_', 'Friday_', 'Saturday_', 'Sunday_']


def fuses_csv():
    # 获取所有csv文件的文件名列表
    csv_files = glob.glob("./Data/CIC_80%/*.csv")

    dfs = []
    num_class = dict()
    num = 0
    # 读取每个csv文件并添加到列表中
    for file in csv_files:
        file_name = file.split('\\')[-1].split('.')[0]
        df = pd.read_csv(file)
        # num_class[file_name] = df.shape[0]
        # count = df.shape[0]
        # print(file_name,':',round(count*0.7),round(count*0.3))
        # num += count
        for day in days_of_week:
            if file_name.startswith(day):
                file_name = file_name.removeprefix(day)
                break
        df['new_column'] = file_name
        dfs.append(df)

    # 使用pandas.concat函数将所有DataFrame合并成一个
    combined_df = pd.concat(dfs, ignore_index=True)

    # 将合并后的DataFrame保存到一个新的csv文件中
    combined_df.to_csv("Data/CIC_80%/benignandmali_data_withlabels_80%.csv", index=False)

    # ratio = 0
    # for key,value in num_class.items():
    #     ratio += value/num
    #     print(key,value/num)
    # print(ratio)
    
    
def choose_data_to_csv():
    train_labels = {
        'Benign': [0,7000,1400,1400], # train_count, test_count
        'DDoS_LOIT': [1,7000,1400,1400],
        'DoS_Slowhttptest': [2,3200,500,500],
        'FTP_Patator': [3,3000,450,450],
        'DoS_Slowloris': [4,3000,400,400],
        'SSH_Patator': [5,2200,350,350]}
    abnormal_labels = {
        'DoS_GoldenEye': [6,1500],
        'Portscan': [6,1500],
        'BruteForce': [6,1000],
        'Botnet': [6,730]}
    CLASS_DICT = {**train_labels, **abnormal_labels}  # 字典解包，合并为一个dict
    
    csv_files = glob.glob("./Data/CICdata/*.csv")
    dfs = []
    num = 0
    for file in csv_files:
        file_name = file.split('\\')[-1].split('.')[0]
        for day in days_of_week:
            if file_name.startswith(day):
                file_name = file_name.removeprefix(day)
                break
        df = pd.read_csv(file)
        df = df.sample(frac=1, random_state=42)
        num = sum(CLASS_DICT[file_name][1:])
        print(file_name,':', num)
        df = df.iloc[0:num]
        df['label'] = file_name
        dfs.append(df)
        
    combined_df = pd.concat(dfs, ignore_index=True)
    combined_df.to_csv("Data/CIC/benignandmali_data_withlabels_new.csv", index=False)  
  
  
# def mali_data_split():
#     path = "Data\CIC\mali_data_withoutlabels.csv"
    
#     csv_files = glob.glob("./Data/CICdata/*.csv")

#     dfs = []
#     for file in csv_files:
#         file_name = file.split('\\')[-1].split('.')[0]
#         df = pd.read_csv(file)
#         for day in days_of_week:
#             if file_name.startswith(day):
#                 file_name = file_name.removeprefix(day)
#                 break
#         if not file_name == 'Benign':
#             dfs.append(df)

#     combined_df = pd.concat(dfs, ignore_index=True)
#     combined_df.to_csv(path, index=False)
  
def normalize(X, min_vals=None, max_vals=None):
    if min_vals is None and max_vals is None:
        # 计算训练集的 min 和 max
        min_vals = np.min(X, axis=0)
        max_vals = np.max(X, axis=0)
        np.save('min_vals.npy',min_vals)
        np.save('max_vals.npy',max_vals)
        # 防止除以零的情况
        range_vals = max_vals - min_vals
        range_vals[range_vals == 0] = 1  # 避免除以零
        X_normalized = (X - min_vals) / range_vals
        return min_vals, max_vals, X_normalized
    else:
        # 使用提供的 min 和 max 进行标准化
        range_vals = max_vals - min_vals
        range_vals[range_vals == 0] = 1  # 避免除以零
        X_normalized = (X - min_vals) / range_vals
        return X_normalized
    

def normalize_data_to_csv():
    try:
        df = pd.read_csv('Data/CIC_80%/benignandmali_data_withlabels_80%.csv')
    except Exception as e:
        print(f"Error loading data: {e}")
        return None, None, None
    
    X = df.iloc[:,6:-1].values
    y = df.iloc[:,-1].values
    min_vals,max_vals,X_n = normalize(X)
    np.save('min_vals_80%.npy',min_vals)
    np.save('max_vals_80%.npy',max_vals)
    
    df_combined = pd.DataFrame(data=X_n, columns=df.columns[6:-1])
    df_combined['Label'] = y   # 将标签列加入合并后的 DataFrame
    # df_combined = df_combined.round(5)
    
    # 重新写入 CSV
    output_csv_file = 'Data/CIC_80%/benignandmali_data_withlabels_80%_normalized.csv'
    df_combined.to_csv(output_csv_file, index=False)
    
    
if __name__ == "__main__":
    # train.train(None)
    normalize_data_to_csv()
    # fuses_csv()