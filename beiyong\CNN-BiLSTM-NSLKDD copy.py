# import torch
# import torch.nn as nn
# import torch.optim as optim
# import torch.utils.data
# from torch.nn import functional as F
# import numpy as np
# from sklearn.model_selection import train_test_split
# from sklearn.preprocessing import LabelEncoder
# from torch.utils.data import TensorDataset, DataLoader
# import pandas as pd
# from sklearn.metrics import accuracy_score, confusion_matrix, precision_score, recall_score, f1_score
# from sklearn.model_selection import StratifiedKFold
# from imblearn.over_sampling import RandomOverSampler
# import matplotlib.pyplot as plt
# import itertools

# # 定义全局常量
# Epoch_num = 9
# RANDOM_STATE = 42
# DEVICE = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

# df = pd.read_csv(r'Data\NSL-KDD\KDDTrain+.txt', header=None)
# qp = pd.read_csv(r'Data\NSL-KDD\KDDTest+.txt', header=None)

# df.columns = ['duration', 'protocol_type', 'service', 'flag', 'src_bytes',
# 'dst_bytes', 'land', 'wrong_fragment', 'urgent', 'hot',
# 'num_failed_logins', 'logged_in', 'num_compromised', 'root_shell',
# 'su_attempted', 'num_root', 'num_file_creations', 'num_shells',
# 'num_access_files', 'num_outbound_cmds', 'is_host_login',
# 'is_guest_login', 'count', 'srv_count', 'serror_rate',
# 'srv_serror_rate', 'rerror_rate', 'srv_rerror_rate', 'same_srv_rate',
# 'diff_srv_rate', 'srv_diff_host_rate', 'dst_host_count',
# 'dst_host_srv_count', 'dst_host_same_srv_rate','dst_host_diff_srv_rate', 'dst_host_same_src_port_rate',
# 'dst_host_srv_diff_host_rate', 'dst_host_serror_rate',
# 'dst_host_srv_serror_rate', 'dst_host_rerror_rate',
# 'dst_host_srv_rerror_rate', 'subclass', 'difficulty_level']

# qp.columns = ['duration', 'protocol_type', 'service', 'flag', 'src_bytes',
# 'dst_bytes', 'land', 'wrong_fragment', 'urgent', 'hot',
# 'num_failed_logins', 'logged_in', 'num_compromised', 'root_shell',
# 'su_attempted', 'num_root', 'num_file_creations', 'num_shells',
# 'num_access_files', 'num_outbound_cmds', 'is_host_login',
# 'is_guest_login', 'count', 'srv_count', 'serror_rate',
# 'srv_serror_rate', 'rerror_rate', 'srv_rerror_rate', 'same_srv_rate',
# 'diff_srv_rate', 'srv_diff_host_rate', 'dst_host_count',
# 'dst_host_srv_count', 'dst_host_same_srv_rate','dst_host_diff_srv_rate', 'dst_host_same_src_port_rate',
# 'dst_host_srv_diff_host_rate', 'dst_host_serror_rate',
# 'dst_host_srv_serror_rate', 'dst_host_rerror_rate',
# 'dst_host_srv_rerror_rate', 'subclass', 'difficulty_level']

# df = df.drop('difficulty_level', 1)
# qp = qp.drop('difficulty_level', 1)
# cols = ['protocol_type','service','flag']

# def one_hot(df, cols):
#     for each in cols:
#         dummies = pd.get_dummies(df[each], prefix=each, drop_first=False)
#         df = pd.concat([df, dummies], axis=1)
#         df = df.drop(each, 1)
#     return df

# combined_data = pd.concat([df, qp])
# combined_data = one_hot(combined_data, cols)

# def normalize(df, cols):
#     result = df.copy()
#     for feature_name in cols:
#         max_value = df[feature_name].max()
#         min_value = df[feature_name].min()
#         if max_value > min_value:
#             result[feature_name] = (df[feature_name] - min_value) / (max_value - min_value)
#     return result

# tmp = combined_data.pop('subclass')
# new_train_df = normalize(combined_data, combined_data.columns)

# classlist = []
# check1 = ("apache2", "back", "land", "neptune", "mailbomb", "pod", "processtable", "smurf", "teardrop", "udpstorm", "worm")
# check2 = ("ipsweep", "mscan", "nmap", "portsweep", "saint", "satan")
# check3 = ("buffer_overflow", "loadmodule", "perl", "ps", "rootkit", "sqlattack", "xterm")
# check4 = ("ftp_write", "guess_passwd", "httptunnel", "imap", "multihop", "named", "phf", "sendmail", "Snmpgetattack", "spy", "snmpguess", "warezclient", "warezmaster", "xlock", "xsnoop")

# DoSCount = 0
# ProbeCount = 0
# U2RCount = 0
# R2LCount = 0
# NormalCount = 0

# for item in tmp:
#     if item in check1:
#         classlist.append("DOS")
#         DoSCount += 1
#     elif item in check2:
#         classlist.append("Probe")
#         ProbeCount += 1
#     elif item in check3:
#         classlist.append("U2R")
#         U2RCount += 1
#     elif item in check4:
#         classlist.append("R2L")
#         R2LCount += 1
#     else:
#         classlist.append("Normal")
#         NormalCount += 1

# new_train_df["Class"] = classlist
# print(new_train_df["Class"].value_counts())

# y_train = new_train_df["Class"]
# combined_data_X = new_train_df.drop('Class', 1)

# class CustomModel(nn.Module):
#     def __init__(self):
#         super(CustomModel, self).__init__()
#         self.conv1d = nn.Conv1d(1, 64, kernel_size=64, padding=31)
#         self.max_pool1d = nn.MaxPool1d(kernel_size=5)
#         self.batch_norm1 = nn.BatchNorm1d(64)
#         self.lstm1 = nn.LSTM(64, 64, bidirectional=True, batch_first=True)
#         self.max_pool2d = nn.MaxPool1d(kernel_size=5)
#         self.batch_norm2 = nn.BatchNorm1d(1)
#         self.lstm2 = nn.LSTM(25, 128, bidirectional=True, batch_first=True)
#         self.dropout = nn.Dropout(p=0.5)
#         self.fc = nn.Linear(256, 5)

#     def forward(self, x):
#         x = F.relu(self.conv1d(x))
#         x = self.max_pool1d(x)
#         x = self.batch_norm1(x)
#         x = x.permute(0, 2, 1)
#         x, _ = self.lstm1(x)
#         x = x[:, -1, :]
#         x = x.unsqueeze(1)
#         x = self.max_pool2d(x)
#         x = self.batch_norm2(x)
#         x, _ = self.lstm2(x)
#         x = x[:, -1, :]
#         x = self.dropout(x)
#         x = self.fc(x)
#         return x

# def plot_confusion_matrix(cm, target_names, title='Confusion matrix', cmap=None, normalize=True):
#     accuracy = np.trace(cm) / float(np.sum(cm))
#     misclass = 1 - accuracy
#     if cmap is None:
#         cmap = plt.get_cmap('Blues')
#     plt.figure(figsize=(8, 6))
#     plt.imshow(cm, interpolation='nearest', cmap=cmap)
#     plt.title(title)
#     plt.colorbar()
#     if target_names is not None:
#         tick_marks = np.arange(len(target_names))
#         plt.xticks(tick_marks, target_names, rotation=45)
#         plt.yticks(tick_marks, target_names)
#     if normalize:
#         cm = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]
#     thresh = cm.max() / 1.5 if normalize else cm.max() / 2
#     for i, j in itertools.product(range(cm.shape[0]), range(cm.shape[1])):
#         if normalize:
#             plt.text(j, i, "{:0.4f}".format(cm[i, j]),
#                      horizontalalignment="center",
#                      color="white" if cm[i, j] > thresh else "black")
#         else:
#             plt.text(j, i, "{:,}".format(cm[i, j]),
#                      horizontalalignment="center",
#                      color="white" if cm[i, j] > thresh else "black")
#     plt.tight_layout()
#     plt.ylabel('True label')
#     plt.xlabel('Predicted label\naccuracy={:0.4f}; misclass={:0.4f}'.format(accuracy, misclass))
#     plt.show()

# def train_and_evaluate_model(N_splits, combined_data_X, y_train):
#     kfold = StratifiedKFold(n_splits=N_splits, shuffle=True, random_state=RANDOM_STATE)
#     oos_pred = []
#     confusion_matrices = []
#     accuracy_scores = []
#     precision_scores = []
#     recall_scores = []
#     f1_scores = []

#     for train_index, test_index in kfold.split(combined_data_X, y_train):
#         train_X, test_X = combined_data_X.iloc[train_index], combined_data_X.iloc[test_index]
#         train_y, test_y = y_train.iloc[train_index], y_train.iloc[test_index]

#         ros = RandomOverSampler(random_state=RANDOM_STATE)
#         train_X, train_y = ros.fit_resample(train_X, train_y)

#         x_columns_train = new_train_df.columns.drop('Class')
#         x_train_array = train_X[x_columns_train].values
#         x_train_1 = np.reshape(x_train_array, (x_train_array.shape[0], 1, x_train_array.shape[1]))

#         dummies = pd.get_dummies(train_y)
#         y_train_1 = dummies.values
#         y_train_target = y_train_1.argmax(axis=1)

#         x_columns_test = new_train_df.columns.drop('Class')
#         x_test_array = test_X[x_columns_test].values
#         x_test_2 = np.reshape(x_test_array, (x_test_array.shape[0], 1, x_test_array.shape[1]))

#         dummies_test = pd.get_dummies(test_y)
#         y_test_2 = dummies_test.values
#         y_test_target = y_test_2.argmax(axis=1)

#         train_dataset = TensorDataset(torch.tensor(x_train_1, dtype=torch.float32),
#                                       torch.tensor(y_train_target, dtype=torch.long))
#         train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)

#         test_dataset = TensorDataset(torch.tensor(x_test_2, dtype=torch.float32),
#                                      torch.tensor(y_test_target, dtype=torch.long))
#         test_loader = DataLoader(test_dataset, batch_size=32, shuffle=False)

#         model = CustomModel().to(DEVICE)
#         criterion = nn.CrossEntropyLoss()
#         optimizer = optim.Adam(model.parameters(), lr=0.001)

#         for epoch in range(Epoch_num):
#             model.train()
#             running_loss = 0.0
#             for inputs, labels in train_loader:
#                 inputs, labels = inputs.to(DEVICE), labels.to(DEVICE)
#                 optimizer.zero_grad()
#                 outputs = model(inputs)
#                 loss = criterion(outputs, labels)
#                 loss.backward()
#                 optimizer.step()
#                 running_loss += loss.item()
#             print(f"Epoch {epoch+1}, Loss: {running_loss/len(train_loader)}")

#         model.eval()
#         with torch.no_grad():
#             input_tensor = torch.tensor(x_test_2, dtype=torch.float32).to(DEVICE)
#             predicted = model(input_tensor)
#             pred = predicted.cpu().numpy()
#         pred = np.argmax(pred, axis=1)
#         y_eval = y_test_target

#         accuracy = accuracy_score(y_eval, pred)
#         precision = precision_score(y_eval, pred, average='macro')
#         recall = recall_score(y_eval, pred, average='macro')
#         f1 = f1_score(y_eval, pred, average='macro')

#         accuracy_scores.append(accuracy)
#         precision_scores.append(precision)
#         recall_scores.append(recall)
#         f1_scores.append(f1)

#         correct = np.sum(pred == y_eval)
#         total = y_eval.size
#         score = correct / total
#         oos_pred.append(score)
#         print(f"Validation score: {score}")

#         cm = confusion_matrix(y_eval, pred, labels=[0, 1, 2, 3, 4])
#         confusion_matrices.append(cm)

#     average_oos_pred = np.mean(oos_pred)
#     print(f"Average OOS prediction score for {N_splits} folds: {average_oos_pred}")

#     avg_confusion_matrix = np.mean(confusion_matrices, axis=0)
#     plot_confusion_matrix(avg_confusion_matrix, 
#                           target_names=["DOS", "Normal", "Probe", "R2L", "U2R"],
#                           title=f"Confusion Matrix for {N_splits} folds")

#     return accuracy_scores, precision_scores, recall_scores, f1_scores

# # 对不同的k值进行多折交叉验证并可视化结果
# k_values = [2, 5, 10]
# results = {}
# for k in k_values:
#     print(f"\nTraining and evaluating with {k}-fold cross-validation")
#     acc, prec, rec, f1 = train_and_evaluate_model(k, combined_data_X, y_train)
#     results[k] = {
#         'accuracy': acc,
#         'precision': prec,
#         'recall': rec,
#         'f1': f1
#     }

# # 可视化不同k值的accuracy、precision、recall和f1
# metrics = ['accuracy', 'precision', 'recall', 'f1']
# for metric in metrics:
#     plt.figure(figsize=(10, 6))
#     for k in k_values:
#         plt.plot(range(1, len(results[k][metric]) + 1), results[k][metric], label=f'k={k}')
#     plt.xlabel('Fold')
#     plt.ylabel(metric.capitalize())
#     plt.title(f'{metric.capitalize()} per Fold for Different k values')
#     plt.legend()
#     plt.show()

# torch.save(model.state_dict(), 'Models/NSLKDD-CNN-BiLSTM-known.pth')
# print("Model saved successfully.")



import torch
import torch.nn as nn
import torch.optim as optim
import torch.utils.data
from torch.nn import functional as F
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder
from torch.utils.data import TensorDataset, DataLoader
import pandas as pd
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score

from sklearn.model_selection import StratifiedKFold
from imblearn.over_sampling import RandomOverSampler
import pdb
# 定义全局常量
Epoch_num = 9
RANDOM_STATE = 42
N_splits = 2
DEVICE = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

df = pd.read_csv(r'Data\NSL-KDD\KDDTrain+.txt', header=None)


qp = pd.read_csv(r'Data\NSL-KDD\KDDTest+.txt', header=None)


df.columns = ['duration', 'protocol_type', 'service', 'flag', 'src_bytes',
'dst_bytes', 'land', 'wrong_fragment', 'urgent', 'hot',
'num_failed_logins', 'logged_in', 'num_compromised', 'root_shell',
'su_attempted', 'num_root', 'num_file_creations', 'num_shells',
'num_access_files', 'num_outbound_cmds', 'is_host_login',
'is_guest_login', 'count', 'srv_count', 'serror_rate',
'srv_serror_rate', 'rerror_rate', 'srv_rerror_rate', 'same_srv_rate',
'diff_srv_rate', 'srv_diff_host_rate', 'dst_host_count',
'dst_host_srv_count', 'dst_host_same_srv_rate','dst_host_diff_srv_rate', 'dst_host_same_src_port_rate',
'dst_host_srv_diff_host_rate', 'dst_host_serror_rate',
'dst_host_srv_serror_rate', 'dst_host_rerror_rate',
'dst_host_srv_rerror_rate', 'subclass', 'difficulty_level']


qp.columns = ['duration', 'protocol_type', 'service', 'flag', 'src_bytes',
'dst_bytes', 'land', 'wrong_fragment', 'urgent', 'hot',
'num_failed_logins', 'logged_in', 'num_compromised', 'root_shell',
'su_attempted', 'num_root', 'num_file_creations', 'num_shells',
'num_access_files', 'num_outbound_cmds', 'is_host_login',
'is_guest_login', 'count', 'srv_count', 'serror_rate',
'srv_serror_rate', 'rerror_rate', 'srv_rerror_rate', 'same_srv_rate',
'diff_srv_rate', 'srv_diff_host_rate', 'dst_host_count',
'dst_host_srv_count', 'dst_host_same_srv_rate','dst_host_diff_srv_rate', 'dst_host_same_src_port_rate',
'dst_host_srv_diff_host_rate', 'dst_host_serror_rate',
'dst_host_srv_serror_rate', 'dst_host_rerror_rate',
'dst_host_srv_rerror_rate', 'subclass', 'difficulty_level']


lst_names = df.columns
testlst_names = qp.columns

df = df.drop('difficulty_level', 1)
qp = qp.drop('difficulty_level', 1)
cols = ['protocol_type','service','flag']
def one_hot(df, cols):
    """
    @param df pandas DataFrame
    @param cols a list of columns to encode
    @return a DataFrame with one-hot encoding
    """
    for each in cols:
        dummies = pd.get_dummies(df[each], prefix=each, drop_first=False)
        df = pd.concat([df, dummies], axis=1)
        df = df.drop(each, 1)
    return df

combined_data = pd.concat([df,qp])
combined_data = one_hot(combined_data,cols)
def normalize(df, cols):
    """
    @param df pandas DataFrame
    @param cols a list of columns to encode
    @return a DataFrame with normalized specified features
    """
    result = df.copy() # do not touch the original df
    for feature_name in cols:
        max_value = df[feature_name].max()
        min_value = df[feature_name].min()
        if max_value > min_value:
            result[feature_name] = (df[feature_name] - min_value) / (max_value - min_value)
    return result

tmp = combined_data.pop('subclass')
new_train_df = normalize(combined_data,combined_data.columns)

classlist = []
check1 = ("apache2","back","land","neptune","mailbomb","pod","processtable","smurf","teardrop","udpstorm","worm")
check2 = ("ipsweep","mscan","nmap","portsweep","saint","satan")
check3 = ("buffer_overflow","loadmodule","perl","ps","rootkit","sqlattack","xterm")
check4 = ("ftp_write","guess_passwd","httptunnel","imap","multihop","named","phf","sendmail","Snmpgetattack","spy","snmpguess","warezclient","warezmaster","xlock","xsnoop")

DoSCount=0
ProbeCount=0
U2RCount=0
R2LCount=0
NormalCount=0

for item in tmp:
    if item in check1:
        classlist.append("DOS")
        DoSCount=DoSCount+1
    elif item in check2:
        classlist.append("Probe")
        ProbeCount=ProbeCount+1
    elif item in check3:
        classlist.append("U2R")
        U2RCount=U2RCount+1
    elif item in check4:
        classlist.append("R2L")
        R2LCount=R2LCount+1
    else:
        classlist.append("Normal")
        NormalCount=NormalCount+1

new_train_df["Class"] = classlist
print(new_train_df["Class"].value_counts())

y_train=new_train_df["Class"]
combined_data_X = new_train_df.drop('Class', 1)
oos_pred = []

from sklearn.model_selection import StratifiedKFold
kfold = StratifiedKFold(n_splits=10,shuffle=True,random_state=42)
kfold.get_n_splits(combined_data_X,y_train)






kfold = StratifiedKFold(n_splits=N_splits, shuffle=True, random_state=RANDOM_STATE)
kfold.get_n_splits(combined_data_X,y_train)

class CustomModel(nn.Module):
    def __init__(self):
        super(CustomModel, self).__init__()
        self.conv1d = nn.Conv1d(1, 64, kernel_size=64, padding=31)
        self.max_pool1d = nn.MaxPool1d(kernel_size=5)
        self.batch_norm1 = nn.BatchNorm1d(64)
        self.lstm1 = nn.LSTM(64, 64, bidirectional=True, batch_first=True)
        
        self.max_pool2d = nn.MaxPool1d(kernel_size=5)
        self.batch_norm2 = nn.BatchNorm1d(1)
        self.lstm2 = nn.LSTM(25, 128, bidirectional=True, batch_first=True)
        self.dropout = nn.Dropout(p=0.5)
        self.fc = nn.Linear(256, 5)
        self.softmax = nn.Softmax(dim=1)

    def forward(self, x):
        x = F.relu(self.conv1d(x))
        # print(x.shape)
        
        x = self.max_pool1d(x)
        x = self.batch_norm1(x)
        
        x = x.permute(0, 2, 1)
        x, _ = self.lstm1(x)
        
        x = x[:, -1, :]
        x = x.unsqueeze(1)
        x = self.max_pool2d(x)
        x = self.batch_norm2(x)
        x, _ = self.lstm2(x)
        x = x[:, -1, :]
        
        x = self.dropout(x)
        x = self.fc(x)
        # pdb.set_trace()
        # x = self.softmax(x)
        return x

model = CustomModel().to(DEVICE)


criterion = nn.CrossEntropyLoss()
optimizer = optim.Adam(model.parameters(), lr=0.001)

# 使用k-fold交叉验证的方法对数据集进行划分，并在每个fold上进行过采样和模型训练的准备
# 参数:
# - combined_data_X: 包含特征数据的DataFrame
# - y_train: 包含目标变量的Series
# 这个函数不返回任何值，它的主要作用是为训练模型做数据准备
for train_index, test_index in kfold.split(combined_data_X, y_train):
    # 根据划分得到的训练集和测试集索引，从总数据集中分离出训练集和测试集数据
    train_X, test_X = combined_data_X.iloc[train_index], combined_data_X.iloc[test_index]
    train_y, test_y = y_train.iloc[train_index], y_train.iloc[test_index]
    
    # 打印训练集和测试集的索引，以及训练集的目标变量分布
    print("train index:",train_index)
    print("test index:",test_index)
    print(train_y.value_counts())
    
    

    # 处理特征数据，为模型输入做准备
    x_columns_train = new_train_df.columns.drop('Class')
    x_train_array = train_X[x_columns_train].values
    x_train_1=np.reshape(x_train_array, (x_train_array.shape[0],1 ,x_train_array.shape[1]))
    
    # 通过one-hot编码处理目标变量，以备多分类任务
    dummies = pd.get_dummies(train_y) # 分类
    outcomes = dummies.columns
    num_classes = len(outcomes)
    y_train_1 = dummies.values
    y_train_target = y_train_1.argmax(axis=1)


    # 对测试集进行相同的特征处理步骤
    x_columns_test = new_train_df.columns.drop('Class')
    x_test_array = test_X[x_columns_test].values
    x_test_2=np.reshape(x_test_array, (x_test_array.shape[0],1 ,x_test_array.shape[1]))
    
    dummies_test = pd.get_dummies(test_y) # 分类
    outcomes_test = dummies_test.columns
    num_classes = len(outcomes_test) # 注意：这里重复计算了类别数，可以优化
    y_test_2 = dummies_test.values
    y_test_target = y_test_2.argmax(axis=1)

    # 使用pdb设置断点，用于调试
    # pdb.set_trace()
    
    # 将数据转换为PyTorch张量，并创建数据加载器，以供模型训练和评估使用
    train_dataset = TensorDataset(torch.tensor(x_train_1, dtype=torch.float32),
                                 torch.tensor(y_train_target, dtype=torch.long))
    train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)
    
    test_dataset = TensorDataset(torch.tensor(x_test_2, dtype=torch.float32),
                                torch.tensor(y_test_target, dtype=torch.long))
    test_loader = DataLoader(test_dataset, batch_size=32, shuffle=False)
    
    # Training loop
    for epoch in range(Epoch_num):  # Assuming 15 epochs
        model.train()
        running_loss = 0.0
        for inputs, labels in train_loader:
            inputs, labels = inputs.to(DEVICE), labels.to(DEVICE)
            optimizer.zero_grad()
            outputs = model(inputs)
            # pdb.set_trace()
            loss = criterion(outputs, labels)
            loss.backward()
            optimizer.step()
            running_loss += loss.item()
        print(f"Epoch {epoch+1}, Loss: {running_loss/len(train_loader)}")
    
    
    model.eval()
    with torch.no_grad():
        input_tensor = torch.tensor(x_test_2, dtype=torch.float32).to(DEVICE)
        predicted = model(input_tensor)
        pred = predicted.cpu().numpy()
    pred = np.argmax(pred,axis=1)
    y_eval = y_test_target
    # pdb.set_trace()
    accuracy = accuracy_score(y_eval, pred)
    precision = precision_score(y_eval, pred, average='macro')
    recall = recall_score(y_eval, pred, average='macro')
    f1 = f1_score(y_eval, pred, average='macro')
    correct = np.sum(pred == y_eval)
    total = y_eval.size
    score = correct / total
    oos_pred.append(score)
    print(f"Validation score: {score:.4f},- Accuracy: {accuracy:.4f}, Precision: {precision:.4f}, Recall: {recall:.4f}, F1: {f1:.4f}")

# Average validation score across folds
print(f"Average OOS prediction score: {np.mean(oos_pred)}")


torch.save(model.state_dict(), 'Models/NSLKDD-CNN-BiLSTM-known.pth')
print("Model saved successfully.")




from sklearn.metrics import confusion_matrix
confussion_matrix=confusion_matrix(y_eval, pred, labels=[0, 1, 2, 3, 4])
print(confussion_matrix)


import numpy as np


def plot_confusion_matrix(cm,
                          target_names,
                          title='Confusion matrix',
                          cmap=None,
                          normalize=True):
    """
    绘制混淆矩阵图。

    参数:
    - cm: 混淆矩阵，一个二维数组，表示分类器的预测结果与真实标签之间的关系。
    - target_names: 目标标签名称的列表，用于在图表中对标签进行标注。
    - title: 图表标题，默认为'Confusion matrix'。
    - cmap: 用于绘制矩阵图的颜色映射，默认为'Blues'。
    - normalize: 是否将混淆矩阵中的值规范化为百分比，默认为True。

    返回值:
    - 无。该函数直接显示绘制的混淆矩阵图。
    """
    
    import matplotlib.pyplot as plt
    import numpy as np
    import itertools

    # 计算准确率和误分类率
    accuracy = np.trace(cm) / float(np.sum(cm))
    misclass = 1 - accuracy

    # 如果未指定颜色映射，使用默认颜色映射
    if cmap is None:
        cmap = plt.get_cmap('Blues')

    # 初始化绘图并设置标题和颜色条
    plt.figure(figsize=(8, 6))
    plt.imshow(cm, interpolation='nearest', cmap=cmap)
    plt.title(title)
    plt.colorbar()

    # 如果提供了目标名称，设置标签轴
    if target_names is not None:
        tick_marks = np.arange(len(target_names))
        plt.xticks(tick_marks, target_names, rotation=45)
        plt.yticks(tick_marks, target_names)

    # 如果需要规范化，将混淆矩阵的值转换为百分比
    if normalize:
        cm = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]


    # 设置文本标签的阈值，以区分高亮和普通文本
    thresh = cm.max() / 1.5 if normalize else cm.max() / 2
    for i, j in itertools.product(range(cm.shape[0]), range(cm.shape[1])):
        if normalize:
            plt.text(j, i, "{:0.4f}".format(cm[i, j]),
                     horizontalalignment="center",
                     color="white" if cm[i, j] > thresh else "black")
        else:
            plt.text(j, i, "{:,}".format(cm[i, j]),
                     horizontalalignment="center",
                     color="white" if cm[i, j] > thresh else "black")

    # 调整图表布局并添加轴标签及准确率、误分类率信息
    plt.tight_layout()
    plt.ylabel('True label')
    plt.xlabel('Predicted label\naccuracy={:0.4f}; misclass={:0.4f}'.format(accuracy, misclass))
    plt.show()


plot_confusion_matrix(cm           = confussion_matrix, 
                      normalize    = False,
                      target_names = ["DOS","Normal","Probe","R2L","U2R"],
                      title        = "Confusion Matrix")