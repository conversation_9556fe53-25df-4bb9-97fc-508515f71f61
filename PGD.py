import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from torch.utils.data import TensorDataset, DataLoader ,Dataset
import matplotlib.pyplot as plt
import pdb
import pickle as pkl
import pandas as pd
from copy import deepcopy


CLASS_DICT = {
    'benign':0,
    'dos_hulk': 1,
    'portscan': 1,
    'ftp_patator': 1,
    'dos_slowloris': 1,
    'dos_slowhttptest': 1,
    'webattack_bruteforce': 1,
    'ddos':1,
    'dos_goldeneye':1,
    'bot':1,
    'ssh_patator':1
}
train_class_num = 2
checkpoint = 'Weight'



class CustomDataset(Dataset):
    def __init__(self, csv_file):
        self.data = pd.read_csv(csv_file)
        self.features = self.data.iloc[:, :75].values
        self.labels = self.data.iloc[:, -1].values

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        features = torch.tensor(self.features[idx], dtype=torch.float32)
        
        label = torch.tensor(CLASS_DICT[self.labels[idx]], dtype=torch.long)

        return features, label

class Classifier(nn.Module):
    def __init__(self, feature_size):
        super(Classifier, self).__init__()
        self.fc1 = nn.Linear(feature_size, 256)
        self.dropout1 = nn.Dropout(0.5)
        self.fc2 = nn.Linear(256, 128)
        self.dropout2 = nn.Dropout(0.5)
        self.fc3 = nn.Linear(128, 64)
        self.dropout3 = nn.Dropout(0.5)
        self.fc4 = nn.Linear(64, 32)
        self.dropout4 = nn.Dropout(0.5)
        self.fc5 = nn.Linear(32, train_class_num)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        x = self.dropout1(self.sigmoid(self.fc1(x)))
        x = self.dropout2(self.sigmoid(self.fc2(x)))
        x = self.dropout3(self.sigmoid(self.fc3(x)))
        x = self.dropout4(self.sigmoid(self.fc4(x)))
        x = self.fc5(x)
        return x

# 准备数据
train_dataset = CustomDataset(r'Data\balance_train_data.csv')
test_dataset = CustomDataset(r'Data\balance_test_abnormal_data.csv')

train_loader = DataLoader(train_dataset, batch_size=128, shuffle=True)
test_loader = DataLoader(test_dataset, batch_size=128, shuffle=True)

model = Classifier(feature_size=75)
# model.load_state_dict(torch.load("Weight\Openmax_DNN_weights.pth"))
criterion = nn.CrossEntropyLoss()
optimizer = optim.Adam(model.parameters(), lr=0.001)

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
model.to(device)


def train(model,train_loader):
    running_loss = 0.0
    correct = 0
    total = 0

    for inputs, labels in train_loader:
        inputs, labels = inputs.to(device), labels.to(device)
        
        optimizer.zero_grad()

        outputs = model(inputs)
        loss = criterion(outputs, labels)
        loss.backward()
        optimizer.step()

        running_loss += loss.item()
        # pdb.set_trace()
        _, predicted = outputs.max(1)
        total += labels.size(0)
        correct += predicted.eq(labels).sum().item()

    train_loss = running_loss / len(train_loader)
    train_acc = 100.0 * correct / total

    print(f'Epoch {epoch+1}/{num_epochs}, Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.2f}%')


def pgd_attack(model, loss_fn, inputs, labels, epsilon=0.01, alpha=0.005, num_steps=100):
    # 使用PGD算法生成对抗性样本
    ori_inputs = deepcopy(inputs)
    
    model.eval()
    for _ in range(num_steps):
        # 前向传播计算损失
        inputs.requires_grad = True
        outputs = model(inputs)
        loss = loss_fn(outputs, labels)
        
        # 反向传播计算梯度并更新输入样本
        model.zero_grad()
        loss.backward()
        
        
        perturbed_inputs = (inputs + alpha * inputs.grad.sign()).detach_()
        
        # 将扰动限制在 epsilon 范围内
        eta = torch.clamp(perturbed_inputs- ori_inputs , min=-epsilon, max=epsilon)
        inputs = torch.clamp(ori_inputs + eta, min=0, max=1).detach_()
        
        # 清零梯度
    
    return inputs

def test(model,test_loader):
    model.eval()
    correct = 0
    total = 0
    scores, labels = [], []
    with torch.no_grad():
        for inputs, targets in test_loader:
            inputs, targets = inputs.to(device), targets.to(device)
            
            outputs = model(inputs)
            _, predicted = outputs.max(1)
            total += targets.size(0)
            correct += predicted.eq(targets).sum().item()
        # pdb.set_trace()
        # num_zeros = targets.numel() - torch.count_nonzero(targets)
        # total += num_zeros
        # correct += torch.count_nonzero((predicted == 0) & (targets == 0)).item()
    test_acc = 100.0 * correct / total
    print(f'Test Acc: {test_acc:.2f}%')


num_epochs = 100
for epoch in range(num_epochs):
    model.train()
    train(model,train_loader)
    if epoch %20 ==0 and epoch >0 :
        torch.save(model.state_dict(), 'Weight/binary_DNN.pth')
        test(model, test_loader)

model.eval()
ori_correct = 0
correct = 0
total = 0
scores, labels = [], []

for inputs, targets in test_loader:
    inputs, targets = inputs.to(device), targets.to(device)
    ori_outputs = model(inputs)
    _, ori_predicted = ori_outputs.max(1)
    ori_correct += ori_predicted.eq(targets).sum().item()

    PGD_inputs = pgd_attack(model,criterion,inputs,targets)
    outputs = model(PGD_inputs)
    _, predicted = outputs.max(1)
    total += targets.size(0)
    correct += predicted.eq(targets).sum().item()
    # pdb.set_trace()
    # num_zeros = targets.numel() - torch.count_nonzero(targets)
    # total += num_zeros
    # correct += torch.count_nonzero((predicted == 0) & (targets == 0)).item()
test_acc = 100.0 * ori_correct / total
adv_test_acc = 100.0 * correct / total
print(f'Test Acc: {test_acc:.2f}%,  Adv Test Acc: {adv_test_acc:.2f}%')