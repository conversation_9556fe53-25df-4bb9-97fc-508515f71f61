import pandas as pd
import numpy as np
from sklearn.preprocessing import LabelEncoder
from sklearn.preprocessing import MinMaxScaler

# def load_data():
    # Default values.
train_set =r'Data\UNSW-NB15\UNSW_NB15_training-set.csv'
test_set =r'Data\UNSW-NB15\UNSW_NB15_testing-set.csv'
train = pd.read_csv(train_set, index_col='id') 
test = pd.read_csv(test_set, index_col='id') 
print(test.shape)
# 二分类数据
training_label = train['label'].values 
testing_label = test['label'].values 
temp_train = training_label
temp_test = testing_label


# Creates new dummy columns from each unique string in a particular feature 创建新的虚拟列
unsw = pd.concat([train, test])
print(unsw.shape)

unsw = pd.get_dummies(data=unsw, columns=['proto', 'service', 'state'])
print(unsw.shape)

# Normalising all numerical features:
unsw.drop(['label', 'attack_cat'], axis=1, inplace=True)
print(unsw.shape)

unsw_value = unsw.values

scaler = MinMaxScaler(feature_range=(0, 1)) 
unsw_value = scaler.fit_transform(unsw_value) 
train_set = unsw_value[:len(train), :] 
test_set = unsw_value[len(train):, :] 

# return train_set, training_label, test_set, testing_label

print(type(test_set),max(testing_label))