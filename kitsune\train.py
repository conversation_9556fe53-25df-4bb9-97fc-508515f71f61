import sys
import os
# wp = os.getcwd()
# print(wp)
# # if wp not in sys.path:
# print(sys.path)


import random
from pathlib import Path
import pandas as pd
import numpy as np
import torch
from kitsune import engine

from kitsune.data import FileFormat, build_input_data_pipe
from kitsune.engine import build_feature_mapper, train_single_epoch
from kitsune.models import Kitsune
import logging
import pdb


device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s',
                    filename=r'Logs\Advdefense_kitsune/log.log',  # 日志文件名
                    filemode='w')
console = logging.StreamHandler()
console.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
console.setFormatter(formatter)
logging.getLogger('').addHandler(console)
    
    
def train(
    input_path: Path,
    batch_size: int = 128,
    file_format: FileFormat = "csv",
    compression_rate: float = 0.1,
    checkpoint_dir: Path = Path("Models/")) -> None:

    random.seed(0)
    torch.manual_seed(0)
    np.random.seed(0)
    
    benign_output_file = Path("Data/CIC/benign_data_withoutlabels.csv")
    other_output_file = Path("Data/CIC/mali_data_withoutlabels.csv")
    if not os.path.exists(benign_output_file):
        try:
            df = pd.read_csv('Data/CIC/benignandmali_data_withlabels_normalized.csv', header=None)
        except Exception as e:
            print(f"Error loading data: {e}")
            return None, None, None
        
        df_benign = df[df.iloc[:, -1] == 'Benign']
        df_other = df[df.iloc[:, -1] != 'Benign']
        df_benign.drop(df_benign.columns[-1], axis=1, inplace=True)
        df_other.drop(df_other.columns[-1], axis=1, inplace=True)
        
        # def is_numeric(val):
        #     return isinstance(val, (int, float))
        # is_all_numeric = df_benign.applymap(is_numeric)
        # logging.info("df_benign is_all_numeric?", is_all_numeric)
        # is_all_numeric = df_other.applymap(is_numeric)
        # logging.info("df_other is_all_numeric?", is_all_numeric)
        # df_benign = df_benign.apply(pd.to_numeric, errors='coerce').fillna(0)
        # df_other = df_other.apply(pd.to_numeric, errors='coerce').fillna(0)
        
        # 保存到CSV文件
        df_benign.to_csv(benign_output_file, index=False, header=None)
        df_other.to_csv(other_output_file, index=False, header=None)
    
    logging.info("Building input data pipe ...")
    dp = build_input_data_pipe(str(benign_output_file),
                               batch_size=batch_size,
                               shuffle=True,
                               file_format=file_format)

    logging.info("Training feature mapper ...")
    feature_mapper = build_feature_mapper(dp,
                                          ds_features=83,
                                          max_features_per_cluster=15)

    model = Kitsune(feature_mapper=feature_mapper,compression_rate=compression_rate)
    model.to(device)

    optimizer = torch.optim.SGD(model.parameters(), lr=2e-3, momentum=0.9)

    logging.info("Training Kitsune ensemble ...")
    for epoch in range(10):
        train_single_epoch(model=model, ds=dp, optimizer=optimizer, device=device,epoch=epoch)

    # pdb.set_trace()
    scores = engine.predict(model, dp, device=device)
    threshold= scores.max()
    logging.info(f"Serializing the model to {checkpoint_dir / 'kitsune_CIC.pt'} ...")
    model.save(threshold,checkpoint_dir / "kitsune_CIC.pt")
    

# if __name__ == "__main__":
#     train(None)
    
