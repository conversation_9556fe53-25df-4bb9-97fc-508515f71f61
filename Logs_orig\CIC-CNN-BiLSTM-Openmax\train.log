2024-05-24 20:08:21,878 - INFO - data:
benign                     1395674
portscan                    160106
dos_hulk                    158987
ddos                         95683
dos_goldeneye                 6765
dos_slowloris                 5674
dos_slowhttptest              4866
ftp_patator                   4003
ssh_patator                   2959
webattack_bruteforce          1360
bot                            735
webattack_xss                  661
webattack_sql_injection         12
heartbleed                      11
Name: dos_hulk, dtype: int64
2024-05-24 20:08:21,882 - INFO - train_data:
benign              5000
ddos                3000
dos_goldeneye       2000
ftp_patator         2000
dos_slowloris       2000
dos_slowhttptest    2000
ssh_patator         2000
Name: dos_hulk, dtype: int64
2024-05-24 20:08:21,887 - INFO - abnormal_data:
dos_hulk                5000
portscan                5000
webattack_bruteforce    1000
bot                      730
Name: dos_hulk, dtype: int64
2024-05-24 20:08:21,893 - INFO - test_abnormal_data:
dos_hulk                5000
portscan                5000
benign                  2000
ddos                    2000
dos_goldeneye           2000
ftp_patator             1588
dos_slowloris           1159
dos_slowhttptest        1100
webattack_bruteforce    1000
ssh_patator              950
bot                      730
Name: dos_hulk, dtype: int64
2024-05-24 20:08:25,067 - INFO - Dataset is ready!!!
2024-05-24 20:08:25,140 - INFO - Starting training...
2024-05-24 20:08:28,495 - INFO - Epoch 1/41, Train Loss: 0.6649, Train Acc: 82.51%, Test Acc: 46.52%, test_total: 22526, test_correct: 10479
2024-05-24 20:08:29,156 - INFO - save plots to Plots\Openmax_CNN-BiLSTM-CIC
2024-05-24 20:08:30,788 - INFO - Epoch 2/41, Train Loss: 0.0915, Train Acc: 97.52%, Test Acc: 47.27%, test_total: 22526, test_correct: 10647
2024-05-24 20:08:32,498 - INFO - Epoch 3/41, Train Loss: 0.0575, Train Acc: 98.47%, Test Acc: 47.35%, test_total: 22526, test_correct: 10665
2024-05-24 20:08:32,927 - INFO - Fittting Weibull distribution...
2024-05-24 20:08:40,758 - INFO - Evaluation...
2024-05-24 20:08:43,305 - INFO - Softmax accuracy is 0.473
2024-05-24 20:08:43,306 - INFO - Softmax precision is 0.297
2024-05-24 20:08:43,306 - INFO - Softmax recall is 0.473
2024-05-24 20:08:43,306 - INFO - Softmax F1 is 0.473
2024-05-24 20:08:43,306 - INFO - Softmax f1_macro is 0.664
2024-05-24 20:08:43,306 - INFO - Softmax f1_macro_weighted is 0.345
2024-05-24 20:08:43,306 - INFO - Softmax area_under_roc is 0.990
2024-05-24 20:08:43,307 - INFO - Softmax classification_report:
{'0': {'precision': 0.2556059624108879, 'recall': 0.986, 'f1-score': 0.4059701492537313, 'support': 2000}, '1': {'precision': 0.3246711060581452, 'recall': 0.9995, 'f1-score': 0.49013117567733233, 'support': 2000}, '2': {'precision': 0.6991119005328597, 'recall': 0.984, 'f1-score': 0.8174454828660436, 'support': 2000}, '3': {'precision': 0.9747847478474785, 'recall': 0.9981108312342569, 'f1-score': 0.986309894212819, 'support': 1588}, '4': {'precision': 0.9537275064267352, 'recall': 0.9603106125970664, 'f1-score': 0.9570077386070507, 'support': 1159}, '5': {'precision': 0.5338899803536346, 'recall': 0.9881818181818182, 'f1-score': 0.6932397959183673, 'support': 1100}, '6': {'precision': 0.9316831683168317, 'recall': 0.9905263157894737, 'f1-score': 0.9602040816326531, 'support': 950}, '7': {'precision': 0.0, 'recall': 0.0, 'f1-score': 0.0, 'support': 11729}, 'accuracy': 0.4734528988724141, 'macro avg': {'precision': 0.5841842964933216, 'recall': 0.8633286972253269, 'f1-score': 0.6637885397709997, 'support': 22526}, 'weighted avg': {'precision': 0.2967452848186165, 'recall': 0.4734528988724141, 'f1-score': 0.34525807288071414, 'support': 22526}}
2024-05-24 20:08:43,307 - INFO - _________________________________________
2024-05-24 20:08:43,307 - INFO - SoftmaxThreshold accuracy is 0.541
2024-05-24 20:08:43,308 - INFO - SoftmaxThreshold precision is 0.829
2024-05-24 20:08:43,308 - INFO - SoftmaxThreshold recall is 0.541
2024-05-24 20:08:43,308 - INFO - SoftmaxThreshold F1 is 0.541
2024-05-24 20:08:43,308 - INFO - SoftmaxThreshold f1_macro is 0.739
2024-05-24 20:08:43,309 - INFO - SoftmaxThreshold f1_macro_weighted is 0.487
2024-05-24 20:08:43,309 - INFO - SoftmaxThreshold area_under_roc is 0.990
2024-05-24 20:08:43,309 - INFO - SoftmaxThreshold classification_report:
{'0': {'precision': 0.2553329864724246, 'recall': 0.9815, 'f1-score': 0.40524360033030554, 'support': 2000}, '1': {'precision': 0.3429993136582018, 'recall': 0.9995, 'f1-score': 0.5107307102708228, 'support': 2000}, '2': {'precision': 0.7771631766100355, 'recall': 0.9835, 'f1-score': 0.8682410064003531, 'support': 2000}, '3': {'precision': 0.9759852216748769, 'recall': 0.9981108312342569, 'f1-score': 0.9869240348692403, 'support': 1588}, '4': {'precision': 0.9769911504424779, 'recall': 0.9525452976704055, 'f1-score': 0.9646133682830931, 'support': 1159}, '5': {'precision': 0.9703237410071942, 'recall': 0.980909090909091, 'f1-score': 0.9755877034358047, 'support': 1100}, '6': {'precision': 0.9400599400599401, 'recall': 0.9905263157894737, 'f1-score': 0.9646335212711431, 'support': 950}, '7': {'precision': 0.9615384615384616, 'recall': 0.13215107852331828, 'f1-score': 0.23236638932613748, 'support': 11729}, 'accuracy': 0.5410636597709314, 'macro avg': {'precision': 0.7750492489329516, 'recall': 0.8773428267658182, 'f1-score': 0.7385425417733625, 'support': 22526}, 'weighted avg': {'precision': 0.8288859940696677, 'recall': 0.5410636597709314, 'f1-score': 0.48693183852475624, 'support': 22526}}
2024-05-24 20:08:43,310 - INFO - _________________________________________
2024-05-24 20:08:43,310 - INFO - OpenMax accuracy is 0.638
2024-05-24 20:08:43,311 - INFO - OpenMax precision is 0.744
2024-05-24 20:08:43,311 - INFO - OpenMax recall is 0.638
2024-05-24 20:08:43,311 - INFO - OpenMax F1 is 0.638
2024-05-24 20:08:43,311 - INFO - OpenMax f1_macro is 0.592
2024-05-24 20:08:43,312 - INFO - OpenMax f1_macro_weighted is 0.633
2024-05-24 20:08:43,312 - INFO - OpenMax area_under_roc is 0.904
2024-05-24 20:08:43,312 - INFO - OpenMax classification_report:
{'0': {'precision': 0.3092369477911647, 'recall': 0.693, 'f1-score': 0.4276457883369331, 'support': 2000}, '1': {'precision': 1.0, 'recall': 0.7495, 'f1-score': 0.8568162332094884, 'support': 2000}, '2': {'precision': 0.9977099236641221, 'recall': 0.6535, 'f1-score': 0.789728096676737, 'support': 2000}, '3': {'precision': 1.0, 'recall': 0.05163727959697733, 'f1-score': 0.09820359281437126, 'support': 1588}, '4': {'precision': 0.9460916442048517, 'recall': 0.3028472821397757, 'f1-score': 0.45882352941176474, 'support': 1159}, '5': {'precision': 0.9984025559105432, 'recall': 0.5681818181818182, 'f1-score': 0.7242178447276942, 'support': 1100}, '6': {'precision': 1.0, 'recall': 0.5410526315789473, 'f1-score': 0.7021857923497267, 'support': 950}, '7': {'precision': 0.631578947368421, 'recall': 0.7345894790689743, 'f1-score': 0.6792006621733476, 'support': 11729}, 'accuracy': 0.6383734351416142, 'macro avg': {'precision': 0.8603775023673879, 'recall': 0.5367885613208117, 'f1-score': 0.5921026924625079, 'support': 22526}, 'weighted avg': {'precision': 0.7437825731923025, 'recall': 0.6383734351416142, 'f1-score': 0.6333199285708813, 'support': 22526}}
2024-05-24 20:08:43,313 - INFO - _________________________________________
2024-05-24 20:08:45,167 - INFO - Epoch 4/41, Train Loss: 0.0432, Train Acc: 98.84%, Test Acc: 47.51%, test_total: 22526, test_correct: 10703
2024-05-24 20:08:45,618 - INFO - Fittting Weibull distribution...
2024-05-24 20:08:53,542 - INFO - Evaluation...
2024-05-24 20:08:56,092 - INFO - Softmax accuracy is 0.475
2024-05-24 20:08:56,092 - INFO - Softmax precision is 0.313
2024-05-24 20:08:56,092 - INFO - Softmax recall is 0.475
2024-05-24 20:08:56,093 - INFO - Softmax F1 is 0.475
2024-05-24 20:08:56,093 - INFO - Softmax f1_macro is 0.695
2024-05-24 20:08:56,093 - INFO - Softmax f1_macro_weighted is 0.355
2024-05-24 20:08:56,093 - INFO - Softmax area_under_roc is 0.990
2024-05-24 20:08:56,093 - INFO - Softmax classification_report:
{'0': {'precision': 0.2297077922077922, 'recall': 0.9905, 'f1-score': 0.3729292168674699, 'support': 2000}, '1': {'precision': 0.33818304855354425, 'recall': 0.9995, 'f1-score': 0.5053722664644167, 'support': 2000}, '2': {'precision': 0.6438356164383562, 'recall': 0.987, 'f1-score': 0.7793130675088827, 'support': 2000}, '3': {'precision': 0.9900062460961899, 'recall': 0.9981108312342569, 'f1-score': 0.9940420194418313, 'support': 1588}, '4': {'precision': 0.9403973509933775, 'recall': 0.9801553062985332, 'f1-score': 0.9598648077735531, 'support': 1159}, '5': {'precision': 0.9748653500897666, 'recall': 0.9872727272727273, 'f1-score': 0.981029810298103, 'support': 1100}, '6': {'precision': 0.9401197604790419, 'recall': 0.991578947368421, 'f1-score': 0.9651639344262295, 'support': 950}, '7': {'precision': 0.0, 'recall': 0.0, 'f1-score': 0.0, 'support': 11729}, 'accuracy': 0.47513983840894963, 'macro avg': {'precision': 0.6321393956072586, 'recall': 0.8667647265217424, 'f1-score': 0.6947143903475608, 'support': 22526}, 'weighted avg': {'precision': 0.31301469504374024, 'recall': 0.47513983840894963, 'f1-score': 0.35524681123135693, 'support': 22526}}
2024-05-24 20:08:56,094 - INFO - _________________________________________
2024-05-24 20:08:56,094 - INFO - SoftmaxThreshold accuracy is 0.527
2024-05-24 20:08:56,094 - INFO - SoftmaxThreshold precision is 0.820
2024-05-24 20:08:56,094 - INFO - SoftmaxThreshold recall is 0.527
2024-05-24 20:08:56,094 - INFO - SoftmaxThreshold F1 is 0.527
2024-05-24 20:08:56,095 - INFO - SoftmaxThreshold f1_macro is 0.730
2024-05-24 20:08:56,095 - INFO - SoftmaxThreshold f1_macro_weighted is 0.459
2024-05-24 20:08:56,095 - INFO - SoftmaxThreshold area_under_roc is 0.990
2024-05-24 20:08:56,095 - INFO - SoftmaxThreshold classification_report:
{'0': {'precision': 0.24924165824064712, 'recall': 0.986, 'f1-score': 0.3979015334947538, 'support': 2000}, '1': {'precision': 0.34584775086505193, 'recall': 0.9995, 'f1-score': 0.5138817480719795, 'support': 2000}, '2': {'precision': 0.7230995225853838, 'recall': 0.9845, 'f1-score': 0.8337920813042556, 'support': 2000}, '3': {'precision': 0.9956030150753769, 'recall': 0.9981108312342569, 'f1-score': 0.9968553459119497, 'support': 1588}, '4': {'precision': 0.9585798816568047, 'recall': 0.9784296807592753, 'f1-score': 0.968403074295474, 'support': 1159}, '5': {'precision': 0.9817518248175182, 'recall': 0.9781818181818182, 'f1-score': 0.9799635701275046, 'support': 1100}, '6': {'precision': 0.9438877755511023, 'recall': 0.991578947368421, 'f1-score': 0.9671457905544147, 'support': 950}, '7': {'precision': 0.9524959742351047, 'recall': 0.10086111347941001, 'f1-score': 0.18240690771721535, 'support': 11729}, 'accuracy': 0.5265027079818876, 'macro avg': {'precision': 0.7688134253783738, 'recall': 0.8771452988778976, 'f1-score': 0.7300437564346934, 'support': 22526}, 'weighted avg': {'precision': 0.8202448375227942, 'recall': 0.5265027079818876, 'f1-score': 0.458702620391582, 'support': 22526}}
2024-05-24 20:08:56,096 - INFO - _________________________________________
2024-05-24 20:08:56,096 - INFO - OpenMax accuracy is 0.751
2024-05-24 20:08:56,097 - INFO - OpenMax precision is 0.808
2024-05-24 20:08:56,097 - INFO - OpenMax recall is 0.751
2024-05-24 20:08:56,097 - INFO - OpenMax F1 is 0.751
2024-05-24 20:08:56,097 - INFO - OpenMax f1_macro is 0.641
2024-05-24 20:08:56,097 - INFO - OpenMax f1_macro_weighted is 0.718
2024-05-24 20:08:56,097 - INFO - OpenMax area_under_roc is 0.904
2024-05-24 20:08:56,098 - INFO - OpenMax classification_report:
{'0': {'precision': 0.6660403945514326, 'recall': 0.709, 'f1-score': 0.6868491160087188, 'support': 2000}, '1': {'precision': 1.0, 'recall': 0.7945, 'f1-score': 0.8854834215658958, 'support': 2000}, '2': {'precision': 0.9985185185185185, 'recall': 0.674, 'f1-score': 0.8047761194029851, 'support': 2000}, '3': {'precision': 1.0, 'recall': 0.0025188916876574307, 'f1-score': 0.005025125628140703, 'support': 1588}, '4': {'precision': 0.945054945054945, 'recall': 0.29680759275237273, 'f1-score': 0.45173998686802364, 'support': 1159}, '5': {'precision': 0.9984101748807631, 'recall': 0.5709090909090909, 'f1-score': 0.7264314632735686, 'support': 1100}, '6': {'precision': 1.0, 'recall': 0.6273684210526316, 'f1-score': 0.7710219922380336, 'support': 950}, '7': {'precision': 0.6934131736526946, 'recall': 0.9379316224742092, 'f1-score': 0.7973472494020439, 'support': 11729}, 'accuracy': 0.7514871703808932, 'macro avg': {'precision': 0.9126796508322943, 'recall': 0.5766294523594953, 'f1-score': 0.6410843092984262, 'support': 22526}, 'weighted avg': {'precision': 0.8076769428038656, 'recall': 0.7514871703808932, 'f1-score': 0.7178103191288446, 'support': 22526}}
2024-05-24 20:08:56,099 - INFO - _________________________________________
2024-05-24 20:08:57,850 - INFO - Epoch 5/41, Train Loss: 0.0388, Train Acc: 98.99%, Test Acc: 47.55%, test_total: 22526, test_correct: 10710
2024-05-24 20:08:58,343 - INFO - Fittting Weibull distribution...
2024-05-24 20:09:06,031 - INFO - Evaluation...
2024-05-24 20:09:08,674 - INFO - Softmax accuracy is 0.475
2024-05-24 20:09:08,675 - INFO - Softmax precision is 0.331
2024-05-24 20:09:08,675 - INFO - Softmax recall is 0.475
2024-05-24 20:09:08,675 - INFO - Softmax F1 is 0.475
2024-05-24 20:09:08,675 - INFO - Softmax f1_macro is 0.708
2024-05-24 20:09:08,676 - INFO - Softmax f1_macro_weighted is 0.365
2024-05-24 20:09:08,676 - INFO - Softmax area_under_roc is 0.991
2024-05-24 20:09:08,676 - INFO - Softmax classification_report:
{'0': {'precision': 0.22993970315398887, 'recall': 0.9915, 'f1-score': 0.3733057228915662, 'support': 2000}, '1': {'precision': 0.2969399881164587, 'recall': 0.9995, 'f1-score': 0.45785616124599177, 'support': 2000}, '2': {'precision': 0.8794483985765125, 'recall': 0.9885, 'f1-score': 0.9307909604519775, 'support': 2000}, '3': {'precision': 0.9949874686716792, 'recall': 1.0, 'f1-score': 0.9974874371859296, 'support': 1588}, '4': {'precision': 0.9342105263157895, 'recall': 0.9801553062985332, 'f1-score': 0.9566315789473685, 'support': 1159}, '5': {'precision': 0.973142345568487, 'recall': 0.9881818181818182, 'f1-score': 0.9806044203879116, 'support': 1100}, '6': {'precision': 0.94662638469285, 'recall': 0.9894736842105263, 'f1-score': 0.9675759135357694, 'support': 950}, '7': {'precision': 0.0, 'recall': 0.0, 'f1-score': 0.0, 'support': 11729}, 'accuracy': 0.4754505904288378, 'macro avg': {'precision': 0.6569118518869708, 'recall': 0.8671638510863597, 'f1-score': 0.7080315243308144, 'support': 22526}, 'weighted avg': {'precision': 0.3305157562606805, 'recall': 0.4754505904288378, 'f1-score': 0.3646681044000715, 'support': 22526}}
2024-05-24 20:09:08,677 - INFO - _________________________________________
2024-05-24 20:09:08,677 - INFO - SoftmaxThreshold accuracy is 0.508
2024-05-24 20:09:08,677 - INFO - SoftmaxThreshold precision is 0.829
2024-05-24 20:09:08,677 - INFO - SoftmaxThreshold recall is 0.508
2024-05-24 20:09:08,678 - INFO - SoftmaxThreshold F1 is 0.508
2024-05-24 20:09:08,678 - INFO - SoftmaxThreshold f1_macro is 0.730
2024-05-24 20:09:08,678 - INFO - SoftmaxThreshold f1_macro_weighted is 0.431
2024-05-24 20:09:08,678 - INFO - SoftmaxThreshold area_under_roc is 0.991
2024-05-24 20:09:08,679 - INFO - SoftmaxThreshold classification_report:
{'0': {'precision': 0.24653428250281004, 'recall': 0.987, 'f1-score': 0.39452383331667834, 'support': 2000}, '1': {'precision': 0.29880418535127057, 'recall': 0.9995, 'f1-score': 0.4600690448791715, 'support': 2000}, '2': {'precision': 0.9110189027201475, 'recall': 0.988, 'f1-score': 0.947949148476853, 'support': 2000}, '3': {'precision': 0.9962287869264613, 'recall': 0.9981108312342569, 'f1-score': 0.9971689210443535, 'support': 1588}, '4': {'precision': 0.9626168224299065, 'recall': 0.9775668679896462, 'f1-score': 0.9700342465753423, 'support': 1159}, '5': {'precision': 0.9792792792792793, 'recall': 0.9881818181818182, 'f1-score': 0.9837104072398188, 'support': 1100}, '6': {'precision': 0.9475806451612904, 'recall': 0.9894736842105263, 'f1-score': 0.9680741503604531, 'support': 950}, '7': {'precision': 0.9443037974683545, 'recall': 0.06360303521186803, 'f1-score': 0.11917884815081077, 'support': 11729}, 'accuracy': 0.5078575867885998, 'macro avg': {'precision': 0.7857958377299401, 'recall': 0.8739295296035144, 'f1-score': 0.7300885750054351, 'support': 22526}, 'weighted avg': {'precision': 0.8285335617775326, 'recall': 0.5078575867885998, 'f1-score': 0.4311665893861206, 'support': 22526}}
2024-05-24 20:09:08,680 - INFO - _________________________________________
2024-05-24 20:09:08,680 - INFO - OpenMax accuracy is 0.695
2024-05-24 20:09:08,681 - INFO - OpenMax precision is 0.780
2024-05-24 20:09:08,681 - INFO - OpenMax recall is 0.695
2024-05-24 20:09:08,681 - INFO - OpenMax F1 is 0.695
2024-05-24 20:09:08,681 - INFO - OpenMax f1_macro is 0.701
2024-05-24 20:09:08,681 - INFO - OpenMax f1_macro_weighted is 0.709
2024-05-24 20:09:08,682 - INFO - OpenMax area_under_roc is 0.904
2024-05-24 20:09:08,682 - INFO - OpenMax classification_report:
{'0': {'precision': 0.3276127612761276, 'recall': 0.7445, 'f1-score': 0.45500381970970205, 'support': 2000}, '1': {'precision': 1.0, 'recall': 0.796, 'f1-score': 0.8864142538975501, 'support': 2000}, '2': {'precision': 0.9986807387862797, 'recall': 0.757, 'f1-score': 0.8612059158134244, 'support': 2000}, '3': {'precision': 1.0, 'recall': 0.39357682619647355, 'f1-score': 0.5648441030275644, 'support': 1588}, '4': {'precision': 0.9650959860383944, 'recall': 0.4771354616048318, 'f1-score': 0.6385681293302541, 'support': 1159}, '5': {'precision': 0.9985007496251874, 'recall': 0.6054545454545455, 'f1-score': 0.7538200339558574, 'support': 1100}, '6': {'precision': 1.0, 'recall': 0.58, 'f1-score': 0.7341772151898733, 'support': 950}, '7': {'precision': 0.695030906317733, 'recall': 0.7381703470031545, 'f1-score': 0.7159513768295709, 'support': 11729}, 'accuracy': 0.6946639438870639, 'macro avg': {'precision': 0.8731151427554653, 'recall': 0.6364796475323757, 'f1-score': 0.7012481059692246, 'support': 22526}, 'weighted avg': {'precision': 0.7795215117078803, 'recall': 0.6946639438870639, 'f1-score': 0.7087980541134135, 'support': 22526}}
2024-05-24 20:09:08,683 - INFO - _________________________________________
2024-05-24 20:09:10,469 - INFO - Epoch 6/41, Train Loss: 0.0313, Train Acc: 99.18%, Test Acc: 47.51%, test_total: 22526, test_correct: 10703
2024-05-24 20:09:10,921 - INFO - Fittting Weibull distribution...
2024-05-24 20:09:18,718 - INFO - Evaluation...
2024-05-24 20:09:21,315 - INFO - Softmax accuracy is 0.475
2024-05-24 20:09:21,315 - INFO - Softmax precision is 0.331
2024-05-24 20:09:21,315 - INFO - Softmax recall is 0.475
2024-05-24 20:09:21,315 - INFO - Softmax F1 is 0.475
2024-05-24 20:09:21,316 - INFO - Softmax f1_macro is 0.708
2024-05-24 20:09:21,316 - INFO - Softmax f1_macro_weighted is 0.365
2024-05-24 20:09:21,316 - INFO - Softmax area_under_roc is 0.990
2024-05-24 20:09:21,316 - INFO - Softmax classification_report:
{'0': {'precision': 0.2301366689830901, 'recall': 0.9935, 'f1-score': 0.37370697761895805, 'support': 2000}, '1': {'precision': 0.2954478273721549, 'recall': 0.9995, 'f1-score': 0.45608031028975593, 'support': 2000}, '2': {'precision': 0.8940697148030783, 'recall': 0.9875, 'f1-score': 0.9384651936326919, 'support': 2000}, '3': {'precision': 0.9869402985074627, 'recall': 0.9993702770780857, 'f1-score': 0.9931163954943679, 'support': 1588}, '4': {'precision': 0.9566326530612245, 'recall': 0.9706643658326143, 'f1-score': 0.9635974304068522, 'support': 1159}, '5': {'precision': 0.956140350877193, 'recall': 0.990909090909091, 'f1-score': 0.9732142857142858, 'support': 1100}, '6': {'precision': 0.94662638469285, 'recall': 0.9894736842105263, 'f1-score': 0.9675759135357694, 'support': 950}, '7': {'precision': 0.0, 'recall': 0.0, 'f1-score': 0.0, 'support': 11729}, 'accuracy': 0.47513983840894963, 'macro avg': {'precision': 0.6582492372871317, 'recall': 0.8663646772537896, 'f1-score': 0.7082195633365851, 'support': 22526}, 'weighted avg': {'precision': 0.33145504362370487, 'recall': 0.47513983840894963, 'f1-score': 0.36491680960286366, 'support': 22526}}
2024-05-24 20:09:21,317 - INFO - _________________________________________
2024-05-24 20:09:21,317 - INFO - SoftmaxThreshold accuracy is 0.483
2024-05-24 20:09:21,318 - INFO - SoftmaxThreshold precision is 0.752
2024-05-24 20:09:21,318 - INFO - SoftmaxThreshold recall is 0.483
2024-05-24 20:09:21,318 - INFO - SoftmaxThreshold F1 is 0.483
2024-05-24 20:09:21,318 - INFO - SoftmaxThreshold f1_macro is 0.717
2024-05-24 20:09:21,319 - INFO - SoftmaxThreshold f1_macro_weighted is 0.386
2024-05-24 20:09:21,319 - INFO - SoftmaxThreshold area_under_roc is 0.990
2024-05-24 20:09:21,319 - INFO - SoftmaxThreshold classification_report:
{'0': {'precision': 0.23018911706694511, 'recall': 0.992, 'f1-score': 0.3736698370844712, 'support': 2000}, '1': {'precision': 0.2983136845246978, 'recall': 0.9995, 'f1-score': 0.4594874152396276, 'support': 2000}, '2': {'precision': 0.9333017975402081, 'recall': 0.9865, 'f1-score': 0.9591638308215849, 'support': 2000}, '3': {'precision': 0.9924859110832811, 'recall': 0.9981108312342569, 'f1-score': 0.9952904238618523, 'support': 1588}, '4': {'precision': 0.9795191451469278, 'recall': 0.9490940465918896, 'f1-score': 0.9640666082383874, 'support': 1159}, '5': {'precision': 0.9723461195361285, 'recall': 0.990909090909091, 'f1-score': 0.9815398469158038, 'support': 1100}, '6': {'precision': 0.9475277497477296, 'recall': 0.988421052631579, 'f1-score': 0.9675425038639877, 'support': 950}, '7': {'precision': 0.7961538461538461, 'recall': 0.01764856338988831, 'f1-score': 0.0345316540161815, 'support': 11729}, 'accuracy': 0.4828642457604546, 'macro avg': {'precision': 0.7687296713499705, 'recall': 0.8652729480945881, 'f1-score': 0.716911515005237, 'support': 22526}, 'weighted avg': {'precision': 0.752142416300177, 'recall': 0.4828642457604546, 'f1-score': 0.38561651152296217, 'support': 22526}}
2024-05-24 20:09:21,320 - INFO - _________________________________________
2024-05-24 20:09:21,320 - INFO - OpenMax accuracy is 0.685
2024-05-24 20:09:21,321 - INFO - OpenMax precision is 0.773
2024-05-24 20:09:21,321 - INFO - OpenMax recall is 0.685
2024-05-24 20:09:21,321 - INFO - OpenMax F1 is 0.685
2024-05-24 20:09:21,321 - INFO - OpenMax f1_macro is 0.681
2024-05-24 20:09:21,322 - INFO - OpenMax f1_macro_weighted is 0.695
2024-05-24 20:09:21,322 - INFO - OpenMax area_under_roc is 0.914
2024-05-24 20:09:21,322 - INFO - OpenMax classification_report:
{'0': {'precision': 0.334339297262341, 'recall': 0.7755, 'f1-score': 0.4672390420244013, 'support': 2000}, '1': {'precision': 1.0, 'recall': 0.778, 'f1-score': 0.875140607424072, 'support': 2000}, '2': {'precision': 0.9979423868312757, 'recall': 0.7275, 'f1-score': 0.8415268941584731, 'support': 2000}, '3': {'precision': 1.0, 'recall': 0.3570528967254408, 'f1-score': 0.5262180974477958, 'support': 1588}, '4': {'precision': 0.945054945054945, 'recall': 0.29680759275237273, 'f1-score': 0.45173998686802364, 'support': 1159}, '5': {'precision': 0.998422712933754, 'recall': 0.5754545454545454, 'f1-score': 0.7301038062283737, 'support': 1100}, '6': {'precision': 1.0, 'recall': 0.7336842105263158, 'f1-score': 0.846387370977535, 'support': 950}, '7': {'precision': 0.683926730631988, 'recall': 0.735356807912013, 'f1-score': 0.7087099424815119, 'support': 11729}, 'accuracy': 0.6848974518334369, 'macro avg': {'precision': 0.8699607590892879, 'recall': 0.622419506671336, 'f1-score': 0.6808832184512733, 'support': 22526}, 'weighted avg': {'precision': 0.773236422681152, 'recall': 0.6848974518334369, 'f1-score': 0.6946042428920605, 'support': 22526}}
2024-05-24 20:09:21,323 - INFO - _________________________________________
2024-05-24 20:09:23,162 - INFO - Epoch 7/41, Train Loss: 0.0314, Train Acc: 99.22%, Test Acc: 47.66%, test_total: 22526, test_correct: 10736
2024-05-24 20:09:23,647 - INFO - Fittting Weibull distribution...
2024-05-24 20:09:31,555 - INFO - Evaluation...
2024-05-24 20:09:34,113 - INFO - Softmax accuracy is 0.477
2024-05-24 20:09:34,113 - INFO - Softmax precision is 0.329
2024-05-24 20:09:34,113 - INFO - Softmax recall is 0.477
2024-05-24 20:09:34,114 - INFO - Softmax F1 is 0.477
2024-05-24 20:09:34,114 - INFO - Softmax f1_macro is 0.708
2024-05-24 20:09:34,115 - INFO - Softmax f1_macro_weighted is 0.365
2024-05-24 20:09:34,115 - INFO - Softmax area_under_roc is 0.991
2024-05-24 20:09:34,115 - INFO - Softmax classification_report:
{'0': {'precision': 0.23036770676255655, 'recall': 0.993, 'f1-score': 0.373976085114396, 'support': 2000}, '1': {'precision': 0.2998799879987999, 'recall': 0.9995, 'f1-score': 0.4613431802446342, 'support': 2000}, '2': {'precision': 0.8605354058721935, 'recall': 0.9965, 'f1-score': 0.9235403151065803, 'support': 2000}, '3': {'precision': 0.9881767268201618, 'recall': 1.0, 'f1-score': 0.9940532081377151, 'support': 1588}, '4': {'precision': 0.9444904722452361, 'recall': 0.9836065573770492, 'f1-score': 0.963651732882502, 'support': 1159}, '5': {'precision': 0.977538185085355, 'recall': 0.9890909090909091, 'f1-score': 0.9832806145503842, 'support': 1100}, '6': {'precision': 0.9457831325301205, 'recall': 0.991578947368421, 'f1-score': 0.9681397738951696, 'support': 950}, '7': {'precision': 0.0, 'recall': 0.0, 'f1-score': 0.0, 'support': 11729}, 'accuracy': 0.4766048122169937, 'macro avg': {'precision': 0.6558464521643029, 'recall': 0.8691595517295474, 'f1-score': 0.7084981137414228, 'support': 22526}, 'weighted avg': {'precision': 0.32936345912666476, 'recall': 0.4766048122169937, 'f1-score': 0.3646670281039938, 'support': 22526}}
2024-05-24 20:09:34,115 - INFO - _________________________________________
2024-05-24 20:09:34,115 - INFO - SoftmaxThreshold accuracy is 0.492
2024-05-24 20:09:34,115 - INFO - SoftmaxThreshold precision is 0.826
2024-05-24 20:09:34,115 - INFO - SoftmaxThreshold recall is 0.492
2024-05-24 20:09:34,115 - INFO - SoftmaxThreshold F1 is 0.492
2024-05-24 20:09:34,116 - INFO - SoftmaxThreshold f1_macro is 0.721
2024-05-24 20:09:34,116 - INFO - SoftmaxThreshold f1_macro_weighted is 0.399
2024-05-24 20:09:34,116 - INFO - SoftmaxThreshold area_under_roc is 0.991
2024-05-24 20:09:34,116 - INFO - SoftmaxThreshold classification_report:
{'0': {'precision': 0.2306350314026518, 'recall': 0.9915, 'f1-score': 0.3742215512360823, 'support': 2000}, '1': {'precision': 0.31074148919633143, 'recall': 0.9995, 'f1-score': 0.47408988497569077, 'support': 2000}, '2': {'precision': 0.8949259092950157, 'recall': 0.9965, 'f1-score': 0.9429855689614385, 'support': 2000}, '3': {'precision': 0.9931163954943679, 'recall': 0.9993702770780857, 'f1-score': 0.9962335216572504, 'support': 1588}, '4': {'precision': 0.9635284139100933, 'recall': 0.9801553062985332, 'f1-score': 0.971770744225834, 'support': 1159}, '5': {'precision': 0.9783978397839784, 'recall': 0.9881818181818182, 'f1-score': 0.9832654907281774, 'support': 1100}, '6': {'precision': 0.9476861167002012, 'recall': 0.991578947368421, 'f1-score': 0.9691358024691358, 'support': 950}, '7': {'precision': 0.9430051813471503, 'recall': 0.031034188762895386, 'f1-score': 0.06009079653322327, 'support': 11729}, 'accuracy': 0.49236437893989166, 'macro avg': {'precision': 0.7827545471412237, 'recall': 0.8722275672112192, 'f1-score': 0.721474170098354, 'support': 22526}, 'weighted avg': {'precision': 0.8258656811774459, 'recall': 0.49236437893989166, 'f1-score': 0.3994482438063007, 'support': 22526}}
2024-05-24 20:09:34,117 - INFO - _________________________________________
2024-05-24 20:09:34,117 - INFO - OpenMax accuracy is 0.807
2024-05-24 20:09:34,117 - INFO - OpenMax precision is 0.841
2024-05-24 20:09:34,118 - INFO - OpenMax recall is 0.807
2024-05-24 20:09:34,119 - INFO - OpenMax F1 is 0.807
2024-05-24 20:09:34,119 - INFO - OpenMax f1_macro is 0.770
2024-05-24 20:09:34,119 - INFO - OpenMax f1_macro_weighted is 0.802
2024-05-24 20:09:34,119 - INFO - OpenMax area_under_roc is 0.926
2024-05-24 20:09:34,120 - INFO - OpenMax classification_report:
{'0': {'precision': 0.6822211852543164, 'recall': 0.731, 'f1-score': 0.7057687665942554, 'support': 2000}, '1': {'precision': 1.0, 'recall': 0.785, 'f1-score': 0.8795518207282913, 'support': 2000}, '2': {'precision': 0.9963715529753265, 'recall': 0.6865, 'f1-score': 0.8129070455891061, 'support': 2000}, '3': {'precision': 1.0, 'recall': 0.5302267002518891, 'f1-score': 0.6930041152263373, 'support': 1588}, '4': {'precision': 0.968503937007874, 'recall': 0.5306298533218292, 'f1-score': 0.6856187290969901, 'support': 1159}, '5': {'precision': 0.9983974358974359, 'recall': 0.5663636363636364, 'f1-score': 0.722737819025522, 'support': 1100}, '6': {'precision': 1.0, 'recall': 0.7052631578947368, 'f1-score': 0.8271604938271605, 'support': 950}, '7': {'precision': 0.752114020731042, 'recall': 0.9403188677636627, 'f1-score': 0.8357519039139166, 'support': 11729}, 'accuracy': 0.8072449613779632, 'macro avg': {'precision': 0.9247010164832493, 'recall': 0.6844127769494692, 'f1-score': 0.7703125867501974, 'support': 22526}, 'weighted avg': {'precision': 0.840693601531252, 'recall': 0.8072449613779632, 'f1-score': 0.8024028260186604, 'support': 22526}}
2024-05-24 20:09:34,120 - INFO - _________________________________________
2024-05-24 20:09:35,914 - INFO - Epoch 8/41, Train Loss: 0.0329, Train Acc: 99.23%, Test Acc: 47.51%, test_total: 22526, test_correct: 10702
2024-05-24 20:09:36,394 - INFO - Fittting Weibull distribution...
