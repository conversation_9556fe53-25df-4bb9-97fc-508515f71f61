'''检查数据'''
import pandas as pd
import numpy as np
import pdb


# 读取CSV文件
df = pd.read_csv('Data/CIC/benignandmali_data_withlabels.csv')
# 移除最后一列
print(df.head())
print(df.iloc[:, -1].value_counts())

df = df.iloc[:,6:-1]

# 将所有列转换为float类型，以处理可能存在的bool类型
df = df.astype(float)

# 检查是否存在NaN值
contains_nan = df.isnull().values.any()

# 检查是否存在无穷大值
contains_infinity = np.isinf(df.values).any()

# 检查过大数据
overlarge_values = False
for col in df.columns:
    if df[col].dtype == 'float64':
        overlarge_values |= (df[col].abs() > np.finfo('float64').max).any()

# 输出检查结果
if contains_nan or contains_infinity or overlarge_values:
    print("CSV文件中存在不合规数据!")
else:
    print("CSV文件中没有发现不合规数据.")