'''MLP model'''
import torch
import torch.nn as nn
import torch.optim as optim
import torch.utils.data
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder
from torch.utils.data import TensorDataset, DataLoader
import pandas as pd
from sklearn.metrics import accuracy_score

# 定义全局常量
BATCH_SIZE = 1024
LEARNING_RATE = 0.001
NUM_EPOCHS = 10
RANDOM_STATE = 42
DEVICE = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

def load_data():
    # 读取CSV文件
    try:
        df = pd.read_csv('Data/benignandmali_data_withlabels.csv', header=None)
    except Exception as e:
        print(f"Error loading data: {e}")
        return None, None, None, None

    # 提取特征和标签
    X = df.iloc[:, :-1].values
    y = df.iloc[:, -1].values

    # 将标签进行数字编码
    label_encoder = LabelEncoder()
    y_encoded = label_encoder.fit_transform(y)

    # 将标签转换为PyTorch张量
    y_tensor = torch.tensor(y_encoded, dtype=torch.long)

    # 划分训练集和测试集
    X_train, X_test, y_train, y_test = train_test_split(X, y_tensor, test_size=0.3, random_state=RANDOM_STATE)

    return X_train, X_test, y_train, y_test,len(label_encoder.classes_)

def prepare_data(X_train, X_test, y_train, y_test):
    # 将数据转换为PyTorch张量并移至GPU
    dataset_train = TensorDataset(torch.tensor(X_train, dtype=torch.float32), torch.tensor(y_train, dtype=torch.long))
    dataset_test = TensorDataset(torch.tensor(X_test, dtype=torch.float32), torch.tensor(y_test, dtype=torch.long))
    
    dataloader_train = DataLoader(dataset_train, batch_size=BATCH_SIZE, shuffle=True)
    dataloader_test = DataLoader(dataset_test, batch_size=BATCH_SIZE, shuffle=False)
    
    return dataloader_train, dataloader_test

class CustomModel(nn.Module):
    def __init__(self, input_size, hidden_size, num_classes):
        super(CustomModel, self).__init__()
        self.fc1 = nn.Linear(input_size, hidden_size)
        self.relu = nn.ReLU()
        self.fc2 = nn.Linear(hidden_size, num_classes)

    def forward(self, x):
        out = self.fc1(x)
        out = self.relu(out)
        out = self.fc2(out)
        return out





def train_model(dataloader_train, dataloader_test, device,input_size,class_size):
    # 定义MLP模型
    model = CustomModel(input_size, 64, class_size).to(device)

    # 定义损失函数和优化器
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.parameters(), lr=LEARNING_RATE)

    for epoch in range(NUM_EPOCHS):
        model.train()
        epoch_loss = 0.0

        for inputs, labels in dataloader_train:
            inputs, labels = inputs.to(device), labels.to(device)

            # 前向传播
            outputs = model(inputs)
            loss = criterion(outputs, labels)

            # 反向传播和优化
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()

            epoch_loss += loss.item()

        # 在测试集上进行预测
        model.eval()
        epoch_loss = 0
        with torch.no_grad():
            correct = 0
            total = 0
            for inputs, labels in dataloader_test:
                inputs, labels = inputs.to(device), labels.to(device)
                outputs = model(inputs)
                _, predicted = torch.max(outputs.data, 1)
                total += labels.size(0)
                correct += (predicted == labels).sum().item()

        accuracy = correct / total
        print(f'Epoch {epoch+1}/{NUM_EPOCHS}, Loss: {epoch_loss/len(dataloader_train):.4f}, Accuracy: {accuracy:.4f}')

    return model

def main():
    X_train, X_test, y_train, y_test,class_size = load_data()
    if X_train is not None:
        dataloader_train, dataloader_test = prepare_data(X_train, X_test, y_train, y_test)
        model = train_model(dataloader_train, dataloader_test, DEVICE,len(X_train[0]),class_size)

if __name__ == "__main__":
    main()