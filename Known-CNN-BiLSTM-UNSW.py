import torch
import torch.nn as nn
import torch.optim as optim
import torch.utils.data
from torch.nn import functional as F
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder
from torch.utils.data import TensorDataset, DataLoader
import pandas as pd
from sklearn.metrics import accuracy_score

from sklearn.model_selection import StratifiedKFold
from imblearn.over_sampling import RandomOverSampler
import pdb
# 定义全局常量
Epoch_num = 15
RANDOM_STATE = 42
N_splits = 10
DEVICE = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

df = pd.read_csv(r'Data\UNSW-NB15\UNSW_NB15_testing-set.csv')
df = df.drop('id', 1)
print(df.shape)
df = df.drop('label', 1)
print(df.head())

qp = pd.read_csv(r'Data\UNSW-NB15\UNSW_NB15_training-set.csv')
#Dropping the last columns of testing set
qp = qp.drop('id', 1)
qp = qp.drop('label', 1)
print(qp.shape)
print(df.isnull().values.any(),qp.isnull().values.any())
cols = ['proto','state','service']
def one_hot(df, cols):
    """
    @param df pandas DataFrame
    @param cols a list of columns to encode
    @return a DataFrame with one-hot encoding
    """
    for each in cols:
        dummies = pd.get_dummies(df[each], prefix=each, drop_first=False)
        df = pd.concat([df, dummies], axis=1)
        df = df.drop(each, 1)
    return df

combined_data = pd.concat([df,qp])
print(combined_data)
# pdb.set_trace()
tmp = combined_data.pop('attack_cat')
combined_data = one_hot(combined_data,cols)
print(combined_data)

def normalize(df, cols):
    """
    @param df pandas DataFrame
    @param cols a list of columns to encode
    @return a DataFrame with normalized specified features
    """
    result = df.copy() # do not touch the original df
    for feature_name in cols:
        max_value = df[feature_name].max()
        min_value = df[feature_name].min()
        if max_value > min_value:
            result[feature_name] = (df[feature_name] - min_value) / (max_value - min_value)
    return result

new_train_df = normalize(combined_data,combined_data.columns)
# print(new_train_df)
# print(tmp)

new_train_df["Class"] = tmp
# print(new_train_df)
# print(new_train_df.isnull().values.any())

y = new_train_df["Class"]
X = new_train_df.drop('Class', 1)
# print(y.shape)

X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
# print(X_train.shape)

combined_data_X = X_train
# print(X_test.shape)
# print(y_train,y_train.isnull().values.any())

oos_pred = []

oversample = RandomOverSampler(sampling_strategy='minority')

kfold = StratifiedKFold(n_splits=N_splits, shuffle=True, random_state=RANDOM_STATE)
kfold.get_n_splits(combined_data_X,y_train)

class CustomModel(nn.Module):
    def __init__(self):
        super(CustomModel, self).__init__()
        self.conv1d = nn.Conv1d(1, 64, kernel_size=64, padding=31)
        self.max_pool1d = nn.MaxPool1d(kernel_size=10)
        self.batch_norm1 = nn.BatchNorm1d(64)
        self.lstm1 = nn.LSTM(64, 64, bidirectional=True, batch_first=True)
        
        self.max_pool2d = nn.MaxPool1d(kernel_size=5)
        self.batch_norm2 = nn.BatchNorm1d(1)
        self.lstm2 = nn.LSTM(25, 128, bidirectional=True, batch_first=True)
        self.dropout = nn.Dropout(p=0.6)
        self.fc = nn.Linear(256, 10)
        self.softmax = nn.Softmax(dim=1)

    def forward(self, x):
        x = F.relu(self.conv1d(x))
        # print(x.shape)
        
        x = self.max_pool1d(x)
        x = self.batch_norm1(x)
        
        x = x.permute(0, 2, 1)
        x, _ = self.lstm1(x)
        
        x = x[:, -1, :]
        x = x.unsqueeze(1)
        x = self.max_pool2d(x)
        x = self.batch_norm2(x)
        x, _ = self.lstm2(x)
        x = x[:, -1, :]
        
        x = self.dropout(x)
        x = self.fc(x)
        # pdb.set_trace()
        # x = self.softmax(x)
        return x

model = CustomModel().to(DEVICE)


criterion = nn.CrossEntropyLoss()
optimizer = optim.Adam(model.parameters(), lr=0.001)

# 使用k-fold交叉验证的方法对数据集进行划分，并在每个fold上进行过采样和模型训练的准备
# 参数:
# - combined_data_X: 包含特征数据的DataFrame
# - y_train: 包含目标变量的Series
# 这个函数不返回任何值，它的主要作用是为训练模型做数据准备
for train_index, test_index in kfold.split(combined_data_X, y_train):
    # 根据划分得到的训练集和测试集索引，从总数据集中分离出训练集和测试集数据
    train_X, test_X = combined_data_X.iloc[train_index], combined_data_X.iloc[test_index]
    train_y, test_y = y_train.iloc[train_index], y_train.iloc[test_index]
    
    # 打印训练集和测试集的索引，以及训练集的目标变量分布
    print("train index:",train_index)
    print("test index:",test_index)
    print(train_y.value_counts())
    
    # 对训练集进行过采样处理，以平衡类别分布
    train_X_over, train_y_over = oversample.fit_resample(train_X, train_y)
    print(train_y_over.value_counts()) # 打印过采样后的训练集目标变量分布

    # 处理特征数据，为模型输入做准备
    x_columns_train = new_train_df.columns.drop('Class')
    x_train_array = train_X_over[x_columns_train].values
    x_train_1=np.reshape(x_train_array, (x_train_array.shape[0],1 ,x_train_array.shape[1]))
    
    # 通过one-hot编码处理目标变量，以备多分类任务
    dummies = pd.get_dummies(train_y_over) # 分类
    outcomes = dummies.columns
    num_classes = len(outcomes)
    y_train_1 = dummies.values
    y_train_target = y_train_1.argmax(axis=1)


    # 对测试集进行相同的特征处理步骤
    x_columns_test = new_train_df.columns.drop('Class')
    x_test_array = test_X[x_columns_test].values
    x_test_2=np.reshape(x_test_array, (x_test_array.shape[0],1 ,x_test_array.shape[1]))
    
    dummies_test = pd.get_dummies(test_y) # 分类
    outcomes_test = dummies_test.columns
    num_classes = len(outcomes_test) # 注意：这里重复计算了类别数，可以优化
    y_test_2 = dummies_test.values
    y_test_target = y_test_2.argmax(axis=1)

    # 使用pdb设置断点，用于调试
    # pdb.set_trace()
    
    # 将数据转换为PyTorch张量，并创建数据加载器，以供模型训练和评估使用
    train_dataset = TensorDataset(torch.tensor(x_train_1, dtype=torch.float32),
                                 torch.tensor(y_train_target, dtype=torch.long))
    train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)
    
    test_dataset = TensorDataset(torch.tensor(x_test_2, dtype=torch.float32),
                                torch.tensor(y_test_target, dtype=torch.long))
    test_loader = DataLoader(test_dataset, batch_size=32, shuffle=False)
    
    # Training loop
    for epoch in range(Epoch_num):  # Assuming 15 epochs
        model.train()
        running_loss = 0.0
        for inputs, labels in train_loader:
            inputs, labels = inputs.to(DEVICE), labels.to(DEVICE)
            optimizer.zero_grad()
            outputs = model(inputs)
            # pdb.set_trace()
            loss = criterion(outputs, labels)
            loss.backward()
            optimizer.step()
            running_loss += loss.item()
        print(f"Epoch {epoch+1}, Loss: {running_loss/len(train_loader)}")
    
    # Evaluation
    model.eval()
    correct = 0
    total = 0
    with torch.no_grad():
        for inputs, labels in test_loader:
            inputs, labels = inputs.to(DEVICE), labels.to(DEVICE)
            outputs = model(inputs)
            _, predicted = torch.max(outputs.data, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()
    
    score = correct / total
    oos_pred.append(score)
    print(f"Validation score: {score}")

# Average validation score across folds
print(f"Average OOS prediction score: {np.mean(oos_pred)}")


torch.save(model.state_dict(), 'Models/UNSW-CNN-BiLSTM-known.pth')
print("Model saved successfully.")


x_columns_train = new_train_df.columns.drop('Class')

model = CustomModel().to(DEVICE)
model.load_state_dict(torch.load('Models/UNSW-CNN-BiLSTM-known.pth'))
x_test_array = X_test[x_columns_train].values
x_test_1=np.reshape(x_test_array, (x_test_array.shape[0], 1,x_test_array.shape[1]))
model.eval()
with torch.no_grad():
    input_tensor = torch.tensor(x_test_1, dtype=torch.float32).to(DEVICE)
    predicted = model(input_tensor)
    pred = predicted.cpu().numpy()

pred = np.argmax(pred,axis=1)
dummies_test = pd.get_dummies(y_test) # Classification
outcomes_test = dummies_test.columns
num_classes = len(outcomes_test)
Y_test = dummies_test.values

y_eval = np.argmax(Y_test,axis=1)


print(y_test.value_counts())
print(dummies_test.columns)

from sklearn.metrics import confusion_matrix
confussion_matrix=confusion_matrix(y_eval, pred, labels=[0, 1, 2, 3, 4, 5,6, 7, 8, 9])
print(confussion_matrix)


import numpy as np


def plot_confusion_matrix(cm,
                          target_names,
                          title='Confusion matrix',
                          cmap=None,
                          normalize=True):
    """
    绘制混淆矩阵图。

    参数:
    - cm: 混淆矩阵，一个二维数组，表示分类器的预测结果与真实标签之间的关系。
    - target_names: 目标标签名称的列表，用于在图表中对标签进行标注。
    - title: 图表标题，默认为'Confusion matrix'。
    - cmap: 用于绘制矩阵图的颜色映射，默认为'Blues'。
    - normalize: 是否将混淆矩阵中的值规范化为百分比，默认为True。

    返回值:
    - 无。该函数直接显示绘制的混淆矩阵图。
    """
    
    import matplotlib.pyplot as plt
    import numpy as np
    import itertools

    # 计算准确率和误分类率
    accuracy = np.trace(cm) / float(np.sum(cm))
    misclass = 1 - accuracy

    # 如果未指定颜色映射，使用默认颜色映射
    if cmap is None:
        cmap = plt.get_cmap('Blues')

    # 初始化绘图并设置标题和颜色条
    plt.figure(figsize=(8, 6))
    plt.imshow(cm, interpolation='nearest', cmap=cmap)
    plt.title(title)
    plt.colorbar()

    # 如果提供了目标名称，设置标签轴
    if target_names is not None:
        tick_marks = np.arange(len(target_names))
        plt.xticks(tick_marks, target_names, rotation=45)
        plt.yticks(tick_marks, target_names)

    # 如果需要规范化，将混淆矩阵的值转换为百分比
    if normalize:
        cm = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]


    # 设置文本标签的阈值，以区分高亮和普通文本
    thresh = cm.max() / 1.5 if normalize else cm.max() / 2
    for i, j in itertools.product(range(cm.shape[0]), range(cm.shape[1])):
        if normalize:
            plt.text(j, i, "{:0.4f}".format(cm[i, j]),
                     horizontalalignment="center",
                     color="white" if cm[i, j] > thresh else "black")
        else:
            plt.text(j, i, "{:,}".format(cm[i, j]),
                     horizontalalignment="center",
                     color="white" if cm[i, j] > thresh else "black")

    # 调整图表布局并添加轴标签及准确率、误分类率信息
    plt.tight_layout()
    plt.ylabel('True label')
    plt.xlabel('Predicted label\naccuracy={:0.4f}; misclass={:0.4f}'.format(accuracy, misclass))
    plt.show()


plot_confusion_matrix(cm           = confussion_matrix, 
                      normalize    = True,
                      target_names = ['Analysis', 'Backdoor', 'DoS', 'Exploits', 'Fuzzers', 'Generic','Normal', 'Reconnaissance', 'Shellcode', 'Worms'],
                      title        = "Confusion Matrix")