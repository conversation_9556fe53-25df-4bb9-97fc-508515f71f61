import numpy as np
import matplotlib.pyplot as plt
from scipy.stats import weibull_min


# 设置默认字体
plt.rcParams['font.family'] = 'serif'
 
# 或者设置中文字体
plt.rcParams['font.family'] = 'SimHei' # 宋体
# 定义形状参数和尺度参数的不同取值
shape_values = [2.5, 5.0, 2.5]
scale_values = [1.0, 1.0, 0.5]

# 创建一个表示横轴的数据范围
x = np.linspace(0, 5, 1000)

# 绘制不同参数值下的 Weibull 分布曲线
plt.figure(figsize=(12, 6))

# 绘制概率密度函数曲线
plt.subplot(1, 2, 1)
for i in range(len(shape_values)):
    shape = shape_values[i]
    scale = scale_values[i]

    # 计算概率密度函数
    pdf = weibull_min.pdf(x, shape, scale)

    plt.plot(x, pdf, label=f'形状={shape}, 尺度={scale}')

plt.xlabel('x')
plt.ylabel('概率密度')
plt.title('Weibull分布概率密度函数')
plt.legend()

# 绘制累积概率密度函数曲线
plt.subplot(1, 2, 2)
for i in range(len(shape_values)):
    shape = shape_values[i]
    scale = scale_values[i]

    # 计算累积概率密度函数
    cdf = weibull_min.cdf(x, shape, scale)

    plt.plot(x, cdf, label=f'形状={shape}, 尺度={scale}')

plt.xlabel('x')
plt.ylabel('累积概率密度')
plt.title('Weibull分布累积概率密度函数')
plt.legend()

# 调整布局
plt.tight_layout()
plt.show()