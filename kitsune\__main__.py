import typer
from pathlib import Path
from kitsune.anomaly_score import compute_anomaly_scores
from kitsune.train import train


kitsune_cli = typer.Typer(name="Kitsune Network")
kitsune_cli.command(name="train")(train)
kitsune_cli.command(name="anomaly-scores")(compute_anomaly_scores)
# @kitsune_cli.command(name="train")
# def train_command(input_path1: Path):
#     train(Path(input_path1))

# @kitsune_cli.command(name="anomaly-scores")
# def anomaly_scores_command(input_path2: Path):
#     compute_anomaly_scores(Path(input_path2))

# @kitsune_cli.command(name="trainandtest")
# def train_and_test_command(input_path1: Path, input_path2: Path):
#     train_command(Path(input_path1))
#     anomaly_scores_command(Path(input_path2))
#     show()

global NEW_LOSS 
NEW_LOSS = True

@kitsune_cli.callback()
def main() -> None:
    """Kitsune anomaly detector Command Line Interface."""


if __name__ == "__main__":
    kitsune_cli()
