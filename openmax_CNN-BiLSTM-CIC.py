import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from torch.utils.data import TensorDataset, DataLoader ,Dataset
import matplotlib.pyplot as plt
import pdb
import pickle as pkl
import pandas as pd
from openmax import compute_train_score_and_mavs_and_dists,fit_weibull,openmax
from evaluation import Evaluation
import os
from Plotter import plot_feature
from cifarutils import progress_bar, Logger
import logging
import argparse
from sklearn.metrics import confusion_matrix


# device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
device = 'cpu'

logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s',
                    filename='Logs/CIC-CNN-BiLSTM-Openmax/train.log',  # 日志文件名
                    filemode='w')
console = logging.StreamHandler()
console.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
console.setFormatter(formatter)
logging.getLogger('').addHandler(console)

np.random.seed(42)
feature_length = 83

train_labels = {
    'Benign': [0,7000,1400,1400], # train_count, test_count
    'DDoS_LOIT': [1,7000,1400,1400],
    'DoS_Slowhttptest': [2,3200,500,500],
    'FTP_Patator': [3,3000,450,450],
    'DoS_Slowloris': [4,3000,400,400],
    'SSH_Patator': [5,2200,350,350]}
abnormal_labels = {
    'DoS_GoldenEye': [6,1500],
    'Portscan': [6,1500],
    'BruteForce': [6,1000],
    'Botnet': [6,730]}
CLASS_DICT = {**train_labels, **abnormal_labels}  # 字典解包，合并为一个dict

train_class_num = 6
checkpoint = 'Models/OpenmaxCIC'
weibull_tail = 5000
weibull_alpha = 6
weibull_threshold = 0.6


def normalize(X, mean=None, std=None):
    if mean is None and std is None:
        mean = np.mean(X, axis=0)
        std = np.std(X, axis=0)
        X = (X-mean)/(std+1e-8)
        return X,mean,std
    else:
        X = (X-mean)/(std+1e-8)
        return X


def calculate_detailed_metrics(y_true, y_pred, method_name=""):
    """计算详细的混淆矩阵指标"""
    cm = confusion_matrix(y_true, y_pred)

    # 对于多分类问题，计算每个类别的TP、FP、TN、FN，然后取平均
    num_classes = cm.shape[0]
    tp_list, fp_list, tn_list, fn_list = [], [], [], []

    for i in range(num_classes):
        tp = cm[i, i]  # 真正例
        fp = cm[:, i].sum() - tp  # 假正例
        fn = cm[i, :].sum() - tp  # 假负例
        tn = cm.sum() - tp - fp - fn  # 真负例

        tp_list.append(tp)
        fp_list.append(fp)
        tn_list.append(tn)
        fn_list.append(fn)

    # 计算平均值
    avg_tp = np.mean(tp_list)
    avg_fp = np.mean(fp_list)
    avg_tn = np.mean(tn_list)
    avg_fn = np.mean(fn_list)

    # 计算总体指标
    total_tp = np.sum(tp_list)
    total_fp = np.sum(fp_list)
    total_tn = np.sum(tn_list)
    total_fn = np.sum(fn_list)

    return {
        'confusion_matrix': cm,
        'avg_tp': avg_tp,
        'avg_fp': avg_fp,
        'avg_tn': avg_tn,
        'avg_fn': avg_fn,
        'total_tp': total_tp,
        'total_fp': total_fp,
        'total_tn': total_tn,
        'total_fn': total_fn,
        'tp_per_class': tp_list,
        'fp_per_class': fp_list,
        'tn_per_class': tn_list,
        'fn_per_class': fn_list
    }
    
    
def dataset_split(processed_data_dir):
    '''分割数据并保存成csv'''
    data = pd.read_csv(os.path.join(processed_data_dir,'benignandmali_data_withlabels_new.csv'))
    data = data.sample(frac=1)  # 随机抽样
    
    train_data = pd.DataFrame()
    val_data = pd.DataFrame()
    test_data = pd.DataFrame()
    # pdb.set_trace()
    for label, [_,train_count, val_count, test_count] in train_labels.items():
        filtered_data = data[data.iloc[:, -1] == label].iloc[:train_count, :]
        # train_data = train_data.append(filtered_data)
        train_data = pd.concat([train_data, filtered_data],ignore_index=True)
        val_filtered_data = data[data.iloc[:, -1] == label].iloc[train_count:val_count+train_count, :]
        val_data = pd.concat([val_data, val_filtered_data],ignore_index=True)
        test_filtered_data = data[data.iloc[:, -1] == label].iloc[val_count+train_count:test_count+val_count+train_count, :]
        test_data = pd.concat([test_data, test_filtered_data],ignore_index=True)
    
    for label, [_,count] in abnormal_labels.items():
        filtered_data = data[data.iloc[:, -1] == label].iloc[:count, :]
        test_data = pd.concat([test_data, filtered_data],ignore_index=True)

    logging.info(f"data:\n{data.iloc[:, -1].value_counts()}")
    logging.info(f"train_data:\n{train_data.iloc[:, -1].value_counts()}")
    logging.info(f"val_data:\n{val_data.iloc[:, -1].value_counts()}")
    logging.info(f"test_data:\n{test_data.iloc[:, -1].value_counts()}")
    
    train_data = train_data.drop(train_data.columns[:6], axis=1)
    val_data = val_data.drop(val_data.columns[:6], axis=1)
    test_data = test_data.drop(test_data.columns[:6], axis=1)
    train_data.iloc[:,:-1], mean, std = normalize(train_data.iloc[:,:-1])
    val_data.iloc[:,:-1] = normalize(val_data.iloc[:,:-1], mean, std)
    test_data.iloc[:,:-1] = normalize(test_data.iloc[:,:-1], mean, std)

    train_data = train_data.sample(frac=1)
    val_data = val_data.sample(frac=1)
    test_data = test_data.sample(frac=1)
    
    train_data.to_csv(os.path.join(processed_data_dir,'balance_train_data.csv'), index=False,header=0)
    val_data.to_csv(os.path.join(processed_data_dir,'balance_val_data.csv'), index=False,header=0)
    test_data.to_csv(os.path.join(processed_data_dir,'balance_test_data.csv'), index=False,header=0)
    logging.info("Dataset is ready!!!")


class CustomDataset(Dataset):
    def __init__(self, csv_file):
        self.data = pd.read_csv(csv_file)
        self.features = self.data.iloc[:, :-1].values
        self.labels = self.data.iloc[:, -1].values

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        features = torch.tensor(self.features[idx], dtype=torch.float32)
        
        label = torch.tensor(CLASS_DICT[self.labels[idx]][0], dtype=torch.long)

        return features, label


class Classifier(nn.Module):
    def __init__(self, input_size, num_classes):
        super(Classifier, self).__init__()
        self.conv1d_1 = nn.Conv1d(1, 64, kernel_size=3)
        self.relu = nn.ReLU()
        self.max_pool1d = nn.MaxPool1d(kernel_size=5, stride=4)
        self.batch_norm1 = nn.BatchNorm1d(num_features=64)
        self.conv1d_2 = nn.Conv1d(64, 1, kernel_size=3, padding=1)  # 1*20
        self.bidir_lstm1 = nn.LSTM(input_size=20, hidden_size=64, bidirectional=True, batch_first=True)
        self.max_pool1d_2 = nn.MaxPool1d(kernel_size=5, stride=5)
        self.batch_norm2 = nn.BatchNorm1d(num_features=1)
        self.bidir_lstm2 = nn.LSTM(input_size=25, hidden_size=128, bidirectional=True, batch_first=True)
        self.dropout = nn.Dropout(p=0.5)
        self.dense = nn.Linear(in_features=256, out_features=num_classes)
        self.softmax = nn.Softmax(dim=1)

    def forward(self, x):
        # pdb.set_trace()
        x = x.unsqueeze(1)
        x = self.conv1d_1(x)
        x = self.relu(x)
        x = self.max_pool1d(x)
        x = self.batch_norm1(x)
        x = self.conv1d_2(x)
        x, _ = self.bidir_lstm1(x)
        x = self.max_pool1d_2(x)
        x = self.batch_norm2(x)
        x, _ = self.bidir_lstm2(x)
        x = self.dropout(x)
        x = self.dense(x)
        x = x.squeeze(1)
        return x


def train(model,train_loader, val_loader, criterion, optimizer, epoch, num_epochs):
    model.train()
    running_loss = 0.0
    correct = 0
    total = 0

    for inputs, labels in train_loader:
        inputs, labels = inputs.to(device), labels.to(device)
        # pdb.set_trace()
        optimizer.zero_grad()

        outputs = model(inputs)
        loss = criterion(outputs, labels)
        loss.backward()
        optimizer.step()

        running_loss += loss.item()
        # pdb.set_trace()
        _, predicted = outputs.max(1)
        total += labels.size(0)
        correct += predicted.eq(labels).sum().item()

    train_loss = running_loss / len(train_loader)
    train_acc = 100.0 * correct / total

    model.eval()
    val_correct = 0
    val_total = 0
    scores, labels = [], []
    with torch.no_grad():
        for inputs, targets in val_loader:
            inputs, targets = inputs.to(device), targets.to(device)

            outputs = model(inputs)
            _, predicted = outputs.max(1)
            val_total += targets.size(0)
            val_correct += predicted.eq(targets).sum().item()
    val_acc = 100.0 * val_correct / val_total
    logging.info(f'Epoch {epoch+1}/{num_epochs}, Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.2f}%, \
        Val Acc: {val_acc:.2f}%, val_total: {val_total}, val_correct: {val_correct}')
    return val_acc


def test(model,epoch, train_loader,test_loader):
    model.eval()
    scores, labels = [], []
    with torch.no_grad():
        for inputs, targets in test_loader:
            inputs, targets = inputs.to(device), targets.to(device)

            outputs = model(inputs)
            scores.append(outputs)
            labels.append(targets)
    
    scores = torch.cat(scores,dim=0).cpu().numpy()
    labels = torch.cat(labels,dim=0).cpu().numpy()
    scores = np.array(scores)[:, np.newaxis, :]
    labels = np.array(labels)
    logging.info("Fittting Weibull distribution...")
    _, mavs, dists = compute_train_score_and_mavs_and_dists(train_class_num, train_loader, device, model)
    # pdb.set_trace()
    categories = list(range(0, train_class_num))
    weibull_model = fit_weibull(mavs, dists, categories, weibull_tail, "euclidean")
    pred_softmax, pred_softmax_threshold, pred_openmax = [], [], []
    score_softmax, score_openmax = [], []
    for score in scores:
        
        so, ss = openmax(weibull_model, categories, score,
                         0.5, weibull_alpha, "euclidean")
        # pdb.set_trace()
        # print(f"so  {so} \n ss  {ss}")# openmax_prob, softmax_prob
        pred_softmax.append(np.argmax(ss))
        pred_softmax_threshold.append(np.argmax(ss) if np.max(ss) >= weibull_threshold else train_class_num)
        pred_openmax.append(np.argmax(so) if np.max(so) >= weibull_threshold else train_class_num)
        score_softmax.append(ss)
        score_openmax.append(so)

    logging.info("Evaluation...")
    labels_str = ['benign', 'ddos', 'dos_slowhttptest', 'ftp_patator', 'dos_slowloris', 'ssh_patator', 'Other_attacks']
    eval_softmax = Evaluation(pred_softmax, labels, score_softmax, savepath=f"Plots/Openmax_CNN-BiLSTM-CIC/confusion_matrix/eval_softmax{epoch}.png", labels_str=labels_str)
    eval_softmax_threshold = Evaluation(pred_softmax_threshold, labels, score_softmax, savepath=f"Plots/Openmax_CNN-BiLSTM-CIC/confusion_matrix/eval_softmax_threshold{epoch}.png", labels_str=labels_str)
    eval_openmax = Evaluation(pred_openmax, labels, score_openmax, savepath=f"Plots/Openmax_CNN-BiLSTM-CIC/confusion_matrix/eval_openmax{epoch}.png", labels_str=labels_str)
    torch.save(eval_softmax, os.path.join(checkpoint, 'eval_softmax.pkl'))
    torch.save(eval_softmax_threshold, os.path.join(checkpoint, 'eval_softmax_threshold.pkl'))
    torch.save(eval_openmax, os.path.join(checkpoint, 'eval_openmax.pkl'))

    # 计算详细的混淆矩阵指标
    detailed_softmax = calculate_detailed_metrics(labels, pred_softmax, "Softmax")
    detailed_softmax_threshold = calculate_detailed_metrics(labels, pred_softmax_threshold, "SoftmaxThreshold")
    detailed_openmax = calculate_detailed_metrics(labels, pred_openmax, "OpenMax")

    # Softmax 结果展示
    logging.info("="*80)
    logging.info("SOFTMAX RESULTS")
    logging.info("="*80)
    logging.info(f"Softmax accuracy is %.3f" % (eval_softmax.accuracy))
    logging.info(f"Softmax precision is %.3f" % (eval_softmax.precision_weighted))
    logging.info(f"Softmax recall is %.3f" % (eval_softmax.recall_weighted))
    logging.info(f"Softmax F1 is %.3f" % (eval_softmax.f1_measure))
    logging.info(f"Softmax f1_macro is %.3f" % (eval_softmax.f1_macro))
    logging.info(f"Softmax f1_macro_weighted is %.3f" % (eval_softmax.f1_macro_weighted))
    logging.info(f"Softmax area_under_roc is %.3f" % (eval_softmax.area_under_roc))

    # 详细混淆矩阵指标
    logging.info("Softmax Confusion Matrix Metrics:")
    logging.info(f"Average TP: {detailed_softmax['avg_tp']:.2f}")
    logging.info(f"Average FP: {detailed_softmax['avg_fp']:.2f}")
    logging.info(f"Average TN: {detailed_softmax['avg_tn']:.2f}")
    logging.info(f"Average FN: {detailed_softmax['avg_fn']:.2f}")
    logging.info(f"Total TP: {detailed_softmax['total_tp']}")
    logging.info(f"Total FP: {detailed_softmax['total_fp']}")
    logging.info(f"Total TN: {detailed_softmax['total_tn']}")
    logging.info(f"Total FN: {detailed_softmax['total_fn']}")

    logging.info(f"Softmax classification_report:\n{eval_softmax.classification_report}")
    logging.info("="*80)

    # SoftmaxThreshold 结果展示
    logging.info("SOFTMAX THRESHOLD RESULTS")
    logging.info("="*80)
    logging.info(f"SoftmaxThreshold accuracy is %.3f" % (eval_softmax_threshold.accuracy))
    logging.info(f"SoftmaxThreshold precision is %.3f" % (eval_softmax_threshold.precision_weighted))
    logging.info(f"SoftmaxThreshold recall is %.3f" % (eval_softmax_threshold.recall_weighted))
    logging.info(f"SoftmaxThreshold F1 is %.3f" % (eval_softmax_threshold.f1_measure))
    logging.info(f"SoftmaxThreshold f1_macro is %.3f" % (eval_softmax_threshold.f1_macro))
    logging.info(f"SoftmaxThreshold f1_macro_weighted is %.3f" % (eval_softmax_threshold.f1_macro_weighted))
    logging.info(f"SoftmaxThreshold area_under_roc is %.3f" % (eval_softmax_threshold.area_under_roc))

    # 详细混淆矩阵指标
    logging.info("SoftmaxThreshold Confusion Matrix Metrics:")
    logging.info(f"Average TP: {detailed_softmax_threshold['avg_tp']:.2f}")
    logging.info(f"Average FP: {detailed_softmax_threshold['avg_fp']:.2f}")
    logging.info(f"Average TN: {detailed_softmax_threshold['avg_tn']:.2f}")
    logging.info(f"Average FN: {detailed_softmax_threshold['avg_fn']:.2f}")
    logging.info(f"Total TP: {detailed_softmax_threshold['total_tp']}")
    logging.info(f"Total FP: {detailed_softmax_threshold['total_fp']}")
    logging.info(f"Total TN: {detailed_softmax_threshold['total_tn']}")
    logging.info(f"Total FN: {detailed_softmax_threshold['total_fn']}")

    logging.info(f"SoftmaxThreshold classification_report:\n{eval_softmax_threshold.classification_report}")
    logging.info("="*80)

    # OpenMax 结果展示
    logging.info("OPENMAX RESULTS")
    logging.info("="*80)
    logging.info(f"OpenMax accuracy is %.3f" % (eval_openmax.accuracy))
    logging.info(f"OpenMax precision is %.3f" % (eval_openmax.precision_weighted))
    logging.info(f"OpenMax recall is %.3f" % (eval_openmax.recall_weighted))
    logging.info(f"OpenMax F1 is %.3f" % (eval_openmax.f1_measure))
    logging.info(f"OpenMax f1_macro is %.3f" % (eval_openmax.f1_macro))
    logging.info(f"OpenMax f1_macro_weighted is %.3f" % (eval_openmax.f1_macro_weighted))
    logging.info(f"OpenMax area_under_roc is %.3f" % (eval_openmax.area_under_roc))

    # 详细混淆矩阵指标
    logging.info("OpenMax Confusion Matrix Metrics:")
    logging.info(f"Average TP: {detailed_openmax['avg_tp']:.2f}")
    logging.info(f"Average FP: {detailed_openmax['avg_fp']:.2f}")
    logging.info(f"Average TN: {detailed_openmax['avg_tn']:.2f}")
    logging.info(f"Average FN: {detailed_openmax['avg_fn']:.2f}")
    logging.info(f"Total TP: {detailed_openmax['total_tp']}")
    logging.info(f"Total FP: {detailed_openmax['total_fp']}")
    logging.info(f"Total TN: {detailed_openmax['total_tn']}")
    logging.info(f"Total FN: {detailed_openmax['total_fn']}")

    logging.info(f"OpenMax classification_report:\n{eval_openmax.classification_report}")
    logging.info("="*80)

    # 综合对比表格
    logging.info("\nCOMPREHENSIVE COMPARISON TABLE")
    logging.info("="*120)
    logging.info(f"{'Method':<20} {'Accuracy':<10} {'Precision':<10} {'Recall':<10} {'F1':<10} {'F1_Macro':<10} {'AUC':<10} {'TP':<8} {'FP':<8} {'TN':<8} {'FN':<8}")
    logging.info("-"*120)

    # Softmax 行
    logging.info(f"{'Softmax':<20} {eval_softmax.accuracy:<10.3f} {eval_softmax.precision_weighted:<10.3f} "
                f"{eval_softmax.recall_weighted:<10.3f} {eval_softmax.f1_measure:<10.3f} "
                f"{eval_softmax.f1_macro:<10.3f} {eval_softmax.area_under_roc:<10.3f} "
                f"{detailed_softmax['avg_tp']:<8.2f} {detailed_softmax['avg_fp']:<8.2f} "
                f"{detailed_softmax['avg_tn']:<8.2f} {detailed_softmax['avg_fn']:<8.2f}")

    # SoftmaxThreshold 行
    logging.info(f"{'SoftmaxThreshold':<20} {eval_softmax_threshold.accuracy:<10.3f} {eval_softmax_threshold.precision_weighted:<10.3f} "
                f"{eval_softmax_threshold.recall_weighted:<10.3f} {eval_softmax_threshold.f1_measure:<10.3f} "
                f"{eval_softmax_threshold.f1_macro:<10.3f} {eval_softmax_threshold.area_under_roc:<10.3f} "
                f"{detailed_softmax_threshold['avg_tp']:<8.2f} {detailed_softmax_threshold['avg_fp']:<8.2f} "
                f"{detailed_softmax_threshold['avg_tn']:<8.2f} {detailed_softmax_threshold['avg_fn']:<8.2f}")

    # OpenMax 行
    logging.info(f"{'OpenMax':<20} {eval_openmax.accuracy:<10.3f} {eval_openmax.precision_weighted:<10.3f} "
                f"{eval_openmax.recall_weighted:<10.3f} {eval_openmax.f1_measure:<10.3f} "
                f"{eval_openmax.f1_macro:<10.3f} {eval_openmax.area_under_roc:<10.3f} "
                f"{detailed_openmax['avg_tp']:<8.2f} {detailed_openmax['avg_fp']:<8.2f} "
                f"{detailed_openmax['avg_tn']:<8.2f} {detailed_openmax['avg_fn']:<8.2f}")

    logging.info("="*120)

    # 打印到控制台
    print("\n" + "="*120)
    print("COMPREHENSIVE COMPARISON TABLE")
    print("="*120)
    print(f"{'Method':<20} {'Accuracy':<10} {'Precision':<10} {'Recall':<10} {'F1':<10} {'F1_Macro':<10} {'AUC':<10} {'TP':<8} {'FP':<8} {'TN':<8} {'FN':<8}")
    print("-"*120)

    print(f"{'Softmax':<20} {eval_softmax.accuracy:<10.3f} {eval_softmax.precision_weighted:<10.3f} "
          f"{eval_softmax.recall_weighted:<10.3f} {eval_softmax.f1_measure:<10.3f} "
          f"{eval_softmax.f1_macro:<10.3f} {eval_softmax.area_under_roc:<10.3f} "
          f"{detailed_softmax['avg_tp']:<8.2f} {detailed_softmax['avg_fp']:<8.2f} "
          f"{detailed_softmax['avg_tn']:<8.2f} {detailed_softmax['avg_fn']:<8.2f}")

    print(f"{'SoftmaxThreshold':<20} {eval_softmax_threshold.accuracy:<10.3f} {eval_softmax_threshold.precision_weighted:<10.3f} "
          f"{eval_softmax_threshold.recall_weighted:<10.3f} {eval_softmax_threshold.f1_measure:<10.3f} "
          f"{eval_softmax_threshold.f1_macro:<10.3f} {eval_softmax_threshold.area_under_roc:<10.3f} "
          f"{detailed_softmax_threshold['avg_tp']:<8.2f} {detailed_softmax_threshold['avg_fp']:<8.2f} "
          f"{detailed_softmax_threshold['avg_tn']:<8.2f} {detailed_softmax_threshold['avg_fn']:<8.2f}")

    print(f"{'OpenMax':<20} {eval_openmax.accuracy:<10.3f} {eval_openmax.precision_weighted:<10.3f} "
          f"{eval_openmax.recall_weighted:<10.3f} {eval_openmax.f1_measure:<10.3f} "
          f"{eval_openmax.f1_macro:<10.3f} {eval_openmax.area_under_roc:<10.3f} "
          f"{detailed_openmax['avg_tp']:<8.2f} {detailed_openmax['avg_fp']:<8.2f} "
          f"{detailed_openmax['avg_tn']:<8.2f} {detailed_openmax['avg_fn']:<8.2f}")

    print("="*120)


def main(args):
    batch_size = args.batch_size
    processed_data_dir = args.data_dir
    num_epochs = args.num_epochs
    lr = args.lr
    save_path = args.save_path
    
    # 准备数据 
    if not os.path.exists(os.path.join(processed_data_dir,'balance_train_data.csv')):
        dataset_split(processed_data_dir)
    train_dataset = CustomDataset(os.path.join(processed_data_dir,'balance_train_data.csv'))
    val_dataset = CustomDataset(os.path.join(processed_data_dir,'balance_val_data.csv'))
    test_dataset = CustomDataset(os.path.join(processed_data_dir,'balance_test_data.csv'))

    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=True)

    # 定义模型、损失函数和优化器
    model = Classifier(feature_length, train_class_num)
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.parameters(), lr=lr, weight_decay=1e-4)

    # 训练模型
    model.to(device)
    if not os.path.exists('Models/Openmax_CNN-BiLSTM-CIC.pth'):
        logging.info('Starting training...')
        best_acc = 0
        for epoch in range(num_epochs):
            val_acc = train(model,train_loader,val_loader, criterion, optimizer, epoch, num_epochs)
            print('{}/{} accuracy:{}'.format(epoch,num_epochs,val_acc))
            if val_acc>best_acc:
                best_acc = val_acc
                torch.save(model.state_dict(), save_path)
    model.load_state_dict(torch.load(save_path, map_location=device))
    test(model, 'test', train_loader, test_loader)


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--data_dir", 
                        type=str, 
                        default='Data/CIC', 
                        help="数据集目录")

    parser.add_argument("--save_path", 
                        type=str, 
                        default='Models/Openmax_CNN-BiLSTM-CIC.pth', 
                        help="模型保存路径")   
    parser.add_argument("--batch_size", type=int, default=1024, help="数据批次大小")
    parser.add_argument("--lr", type=float, default=0.001, help="学习率")
    parser.add_argument("--num_epochs", type=int, default=30, help="训练轮数")
    args = parser.parse_args()
    
    main(args)