# import json
# import matplotlib.pyplot as plt

# # 读取第一个 JSON 文件
# json_file1 = 'Scores_dir\scores_test.json'
# json_file2 = 'Scores_dir\scores_train_ben.json'


# scores1 = []

# with open(json_file1, 'r') as f1:
#     for line in f1:
#         data = json.loads(line)
#         score = data['score']
#         scores1.append(score)

# # 读取第二个 JSON 文件


# scores2 = []

# with open(json_file2, 'r') as f2:
#     for line in f2:
#         data = json.loads(line)
#         score = data['score']
#         scores2.append(score)

# # 绘制图表
# plt.plot(scores1, label='File 1')
# plt.plot(scores2, label='File 2')
# plt.xlabel('Line')
# plt.ylabel('Score')
# plt.title('Score Distribution')
# plt.legend()
# plt.show()

import json
import matplotlib.pyplot as plt

def show():
# 读取第一个 JSON 文件
    json_file1 = 'Scores_dir\scores_malious_data.json'
    json_file2 = 'Scores_dir\scores_benign_data.json'

    

    scores1 = []

    with open(json_file1, 'r') as f1:
        for line in f1:
            data = json.loads(line)
            score = data['score']
            scores1.append(score)


    scores2 = []

    with open(json_file2, 'r') as f2:
        for line in f2:
            data = json.loads(line)
            score = data['score']
            scores2.append(score)
    sorted_scores = sorted(scores2)
    index = int(0.95 * len(sorted_scores))
    if index < len(sorted_scores):
        threshold = sorted_scores[index]
        print("threshold",threshold)

    above_threshold = [score for score in scores1 if score > threshold]
    count = len(above_threshold)
    ratio = count / len(scores1)

    print(f"Test:Number of elements above threshold: {count}")
    print(f"Test:Number of Test: {len(scores1)}")
    print(f"Test:Ratio of elements above threshold: {ratio:.2%}")
    
    # 取行数较少的值
    N = min(len(scores1), len(scores2))

    # 绘制图表
    plt.plot(scores1[:N], label='Malicious')
    plt.plot(scores2[:N], label='Benign')
    plt.axhline(y=threshold, color='r', linestyle='--', label='Threshold')
    plt.xlabel('Line')
    plt.ylabel('Score')
    plt.title('Score Distribution')
    plt.legend()
    plt.savefig('img/score_distribution.png')

    plt.show()
    

if __name__ == "__main__":
    show()