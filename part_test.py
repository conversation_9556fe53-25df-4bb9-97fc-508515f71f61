import sys
import torch
import pandas as pd
import numpy as np
from torch.utils.data import TensorDataset, DataLoader
from . import Known_CNN_BiLSTM_CIC
from . import data_process
 

def load_data(path):
    try:
        df = pd.read_csv(path)
    except Exception as e:
        print(f"Error loading data: {e}")
        return None, None, None

    X = df.iloc[:, :-1].values
    y = df.iloc[:, -1].values
    return X, y


def preprocess_data(path):
    try:
        df = pd.read_csv(path)
    except Exception as e:
        print(f"Error loading data: {e}")
        return None, None, None

    X = df.iloc[:, 6:-1].values
    y = np.ones_like(len(X))*6
    X_normalized, y_normalized = data_process.normalize(X,_,_)
    
    df_normalized = pd.DataFrame(X_normalized, columns=df.columns[6:-1])
    df_normalized['label'] = y_normalized  # 添加归一化后的标签列

    # 将归一化后的DataFrame写入新的CSV文件
    output_path = path.replace('.csv','_normalized.csv')
    df_normalized.to_csv(output_path, index=False)
    print(f"Normalized data saved to {output_path}")
   
    
device = 'cuda' if torch.cuda.is_available() else 'cpu'
model = Known_CNN_BiLSTM_CIC.CustomModel(83, )
model.load_state_dict(torch.load())
model.to(device)

data_path = 'test_data/.csv'
X, y = load_data(data_path)
dataset = TensorDataset(torch.tensor(X, dtype=torch.float32), torch.tensor(y, dtype=torch.long))
dataloader = DataLoader(dataset, batch_size=32, shuffle=True)

correct_num = 0
for data in dataloader:
    x, label = data
    x, label = x.to(device), y.to(device)

    out = model(x)
    pred = torch.argmax(out,dim=1)
    correct_num += sum(label==pred)

accuracy = correct_num / len(dataloader)
print('accuracy:',accuracy)