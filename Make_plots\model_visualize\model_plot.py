import torch
import torch.nn as nn
import torch.optim as optim
import torch.utils.data
from torch.nn import functional as F
import numpy as np

class CustomModel(nn.Module):
    def __init__(self):
        super(CustomModel, self).__init__()
        self.conv1d = nn.Conv1d(1, 64, kernel_size=64, padding=31)
        self.max_pool1d = nn.MaxPool1d(kernel_size=10)
        self.batch_norm1 = nn.BatchNorm1d(64)
        self.lstm1 = nn.LSTM(64, 64, bidirectional=True, batch_first=True)
        
        self.max_pool2d = nn.MaxPool1d(kernel_size=5)
        self.batch_norm2 = nn.BatchNorm1d(1)
        self.lstm2 = nn.LSTM(25, 128, bidirectional=True, batch_first=True)
        self.dropout = nn.Dropout(p=0.6)
        self.fc = nn.Linear(256, 10)
        self.softmax = nn.Softmax(dim=1)

    def forward(self, x):
        x = F.relu(self.conv1d(x))
        # print(x.shape)
        
        x = self.max_pool1d(x)
        x = self.batch_norm1(x)
        
        x = x.permute(0, 2, 1)
        x, _ = self.lstm1(x)
        
        x = x[:, -1, :]
        x = x.unsqueeze(1)
        x = self.max_pool2d(x)
        x = self.batch_norm2(x)
        x, _ = self.lstm2(x)
        x = x[:, -1, :]
        
        x = self.dropout(x)
        x = self.fc(x)
        # pdb.set_trace()
        # x = self.softmax(x)
        return x

import torch
from torchviz import make_dot

# 创建一个随机输入张量
dummy_input = torch.randn(1, 1, 196)  # 假设输入的尺寸是 (batch_size, channels, sequence_length)

model = CustomModel()
# 将模型设置为评估模式
model.eval()

# 进行前向传播，获取输出
output = model(dummy_input)

# 创建计算图
dot = make_dot(output, params=dict(list(model.named_parameters())))

# 可视化模型结构
dot.view()