1. k-fold ------------StratifiedKFold

~~~python
from sklearn.model_selection import StratifiedKFold
from sklearn.metrics import accuracy_score
from sklearn.ensemble import RandomForestClassifier

# 示例数据集
X = ...
y = ...

# 定义 Stratified K-Fold
skf = StratifiedKFold(n_splits=5)

accuracies = []

# 交叉验证
for train_index, test_index in skf.split(X, y):
    X_train, X_test = X[train_index], X[test_index]
    y_train, y_test = y[train_index], y[test_index]
    
    # 训练模型
    model = RandomForestClassifier()
    model.fit(X_train, y_train)
    
    # 预测并评估
    y_pred = model.predict(X_test)
    accuracy = accuracy_score(y_test, y_pred)
    accuracies.append(accuracy)

# 计算平均准确率
mean_accuracy = sum(accuracies) / len(accuracies)
print(f'Mean accuracy: {mean_accuracy:.2f}')

~~~

# 问题：
内容4中，利用PGD进行对抗训练，又用PGD测试模型对对抗样本的鲁棒性