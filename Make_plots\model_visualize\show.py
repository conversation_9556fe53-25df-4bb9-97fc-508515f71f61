import torch
import torch.nn as nn

class MyModel(nn.Module):
    def __init__(self):
        super(MyModel, self).__init__()
        
        self.conv1d_1 = nn.Conv1d(1, 64, kernel_size=3, padding=1)
        self.max_pooling1d_2 = nn.MaxPool1d(kernel_size=5, stride=5)
        self.batch_normalization_2 = nn.BatchNorm1d(64)
        self.conv1d_2 = nn.Conv1d(64, 1, kernel_size=3, padding=1)
        self.bidirectional_2 = nn.LSTM(15, 64, bidirectional=True, batch_first=True)
        self.max_pooling1d_3 = nn.MaxPool1d(kernel_size=5, stride=5)
        self.batch_normalization_3 = nn.BatchNorm1d(1)
        self.bidirectional_3 = nn.LSTM(25, 128, bidirectional=True, batch_first=True)
        self.dropout_1 = nn.Dropout(0.5)
        self.dense_1 = nn.Linear(256, 2)
        self.activation_1 = nn.Softmax(dim=1)

    def forward(self, x):
        x = self.conv1d_1(x)
        print("Conv1D output shape:", x.shape)
        
        x = self.max_pooling1d_2(x)
        print("MaxPooling1D output shape:", x.shape)
        
        x = self.batch_normalization_2(x)
        print("BatchNormalization2 output shape:", x.shape)

        x = self.conv1d_2(x)
        print("Conv1D output shape:", x.shape)
        
        x, _= self.bidirectional_2(x)
        print("Bidirectional2 output shape:", x.shape)
        
        # x = x.view(-1, 128, 1)
        # print("Reshape output shape:", x.shape)
        
        x = self.max_pooling1d_3(x)
        print("MaxPooling1D output shape:", x.shape)
        
        x = self.batch_normalization_3(x)
        print("BatchNormalization3 output shape:", x.shape)
        
        x, _= self.bidirectional_3(x)
        print("Bidirectional3 output shape:", x.shape)
        
        x = self.dropout_1(x)
        print("Dropout output shape:", x.shape)
        
        x = x.view(x.size(0), -1)
        print("Reshape output shape:", x.shape)
        
        x = self.dense_1(x)
        print("Dense output shape:", x.shape)
        
        x = self.activation_1(x)
        print("Activation output shape:", x.shape)
        
        return x

# 创建模型实例
model = MyModel()

# 创建随机输入张量
input = torch.randn(1024, 1, 76)

# 执行前向传播
output = model(input)